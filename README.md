# 🏭 PLC 能源管理系統 2.0

[![Vue 3](https://img.shields.io/badge/Vue-3.2.45-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
[![Node.js](https://img.shields.io/badge/Node.js-18.16.1-339933?style=flat-square&logo=node.js)](https://nodejs.org/)
[![Ant Design Vue](https://img.shields.io/badge/Ant%20Design%20Vue-3.2.15-0170FE?style=flat-square&logo=ant-design)](https://antdv.com/)
[![License](https://img.shields.io/badge/License-MIT-blue?style=flat-square)](LICENSE)

> 基於 Vue 3 的現代化能源管理系統，提供即時監控、數據分析、設備管理等完整功能

## 📋 目錄
- [專案概述](#專案概述)
- [功能特色](#功能特色)
- [技術棧](#技術棧)
- [環境要求](#環境要求)
- [快速開始](#快速開始)
- [專案結構](#專案結構)
- [開發指南](#開發指南)
- [部署說明](#部署說明)
- [API 文檔](#api-文檔)
- [貢獻指南](#貢獻指南)

## 🎯 專案概述

PLC 能源管理系統 2.0 是一個現代化的工業能源監控與管理平台，專為企業級用戶設計。系統提供即時數據監控、歷史數據分析、設備管理、告警系統等核心功能，幫助企業實現智能化能源管理。

### 主要目標
- 🔍 **即時監控**：提供設備狀態、能耗數據的即時監控
- 📊 **數據分析**：強大的歷史數據分析和報表功能
- ⚡ **告警系統**：智能告警機制，及時發現異常狀況
- 🛠️ **設備管理**：完整的設備生命週期管理
- 👥 **權限控制**：細粒度的用戶權限管理系統

## ✨ 功能特色

### 🖥️ 儀表板
- 即時數據展示
- 可自定義的儀表板佈局
- 多種圖表類型支援
- 響應式設計

### 📈 數據分析
- 歷史數據查詢與分析
- 趨勢圖表展示
- 數據匯出功能
- 自定義報表生成

### 🔔 告警管理
- 即時告警通知
- 告警等級分類
- 告警歷史記錄
- 告警規則配置

### 🏗️ 設備管理
- 設備資訊管理
- 設備狀態監控
- 設備分組管理
- 設備維護記錄

### 👤 用戶管理
- 多角色權限控制
- 用戶組管理
- 操作日誌記錄
- 安全認證機制

## 🛠️ 技術棧

### 前端框架
- **Vue 3.2.45** - 漸進式 JavaScript 框架
- **Vue Router 4** - 官方路由管理器
- **Vuex 4** - 狀態管理模式

### UI 框架
- **Ant Design Vue 3.2.15** - 企業級 UI 設計語言
- **Naive UI** - Vue 3 友好的 UI 庫（重構中）
- **Tailwind CSS** - 實用優先的 CSS 框架（重構中）

### 圖表與可視化
- **ApexCharts** - 現代化圖表庫
- **Chart.js** - 簡單靈活的圖表庫
- **ECharts** - 強大的數據可視化庫

### 開發工具
- **Vue CLI 5** - Vue.js 開發工具
- **ESLint** - 代碼品質檢查
- **Prettier** - 代碼格式化
- **Jest** - JavaScript 測試框架

### 其他依賴
- **Axios** - HTTP 客戶端
- **Day.js** - 輕量級日期處理庫
- **MQTT.js** - MQTT 協議客戶端
- **Lodash** - JavaScript 實用工具庫

## ⚠️ 環境要求

> **重要：每一次啟動、開發、部署本專案前，所有人員都必須優先閱讀本說明，並嚴格遵守下列 Node.js 與 npm 版本條件，否則請勿進行任何操作。**

### 必要環境
- **Node.js**: 18.16.1 （必須）
- **npm**: 9.5.1 （必須）
- **Git**: 最新版本
- **現代瀏覽器**: Chrome 90+, Firefox 88+, Safari 14+

### 推薦開發工具
- **VS Code** - 推薦的代碼編輯器
- **Vue DevTools** - Vue.js 開發者工具
- **Postman** - API 測試工具

## 🚀 快速開始

### 1. 環境設置
```bash
# 安裝指定版本的 Node.js
nvm install 18.16.1
nvm alias default 18.16.1
nvm use 18.16.1

# 驗證版本
node -v   # ✅ v18.16.1
npm -v    # ✅ v9.5.1
```

### 2. 克隆專案
```bash
git clone https://github.com/ococomtw/plc-frontend.git
cd plc-frontend
```

### 3. 安裝依賴
```bash
npm install
```

### 4. 環境配置
```bash
# 複製環境變數範本
cp .env.example .env.local

# 編輯環境變數
vim .env.local
```

### 5. 啟動開發服務器
```bash
npm run serve
```

### 6. 建置生產版本
```bash
npm run build
```

## 📁 專案結構

```
plc-frontend/
├── public/                 # 靜態資源
├── src/
│   ├── components/         # 可重用組件
│   │   ├── base/          # 基礎組件（重構中）
│   │   ├── cards/         # 卡片組件
│   │   ├── charts/        # 圖表組件
│   │   └── ...
│   ├── composable/        # 組合式函數
│   ├── config/            # 配置文件
│   ├── layout/            # 佈局組件
│   ├── routes/            # 路由配置
│   ├── view/              # 頁面組件
│   ├── vuex/              # 狀態管理
│   └── main.js            # 應用入口
├── docs/                  # 專案文檔
│   ├── UI-GUIDE.md        # UI 設計指南
│   ├── api-documentation.yaml  # API 文檔
│   └── ...
├── tests/                 # 測試文件
├── package.json           # 專案配置
└── vue.config.js          # Vue CLI 配置
```

## 💻 開發指南

### 代碼規範
- 遵循 ESLint 配置的代碼風格
- 使用 Prettier 進行代碼格式化
- 組件命名使用 PascalCase
- 文件命名使用 kebab-case

### Git 工作流程
```bash
# 創建功能分支
git checkout -b feature/your-feature-name

# 提交變更
git add .
git commit -m "feat: add new feature"

# 推送分支
git push origin feature/your-feature-name

# 創建 Pull Request
```

### 測試
```bash
# 運行單元測試
npm run test

# 運行測試覆蓋率
npm run test:coverage

# 運行 E2E 測試
npm run test:e2e
```

### 代碼檢查
```bash
# ESLint 檢查
npm run lint

# 修復 ESLint 問題
npm run lint:fix
```

## 🚀 部署說明

### 開發環境部署
```bash
# 啟動開發服務器
npm run serve
```

### 測試環境部署
```bash
# 建置測試版本
npm run build:staging

# 使用 Docker 部署
docker build -t plc-frontend:staging .
docker run -p 8080:80 plc-frontend:staging
```

### 生產環境部署
```bash
# 建置生產版本
npm run build

# 使用 Nginx 部署
cp -r dist/* /var/www/html/
systemctl reload nginx
```

### Docker 部署
```bash
# 建置 Docker 映像
docker build -t plc-frontend:latest .

# 運行容器
docker run -d -p 80:80 --name plc-frontend plc-frontend:latest

# 使用 Docker Compose
docker-compose up -d
```

### 環境變數配置
```bash
# 開發環境
VUE_APP_API_BASE_URL=http://localhost:8080/api
VUE_APP_MQTT_URL=ws://localhost:9001
VUE_APP_ENV=development

# 生產環境
VUE_APP_API_BASE_URL=https://api.plc.ococom.tw
VUE_APP_MQTT_URL=wss://mqtt.plc.ococom.tw
VUE_APP_ENV=production
```

## 📚 API 文檔

### Swagger UI
- **開發環境**: http://localhost:8080/swagger-ui
- **生產環境**: https://api.plc.ococom.tw/swagger-ui

### API 文檔文件
- [OpenAPI 3.0 規範](./docs/api-documentation.yaml)
- [API 使用指南](./docs/api-guide.md)

### 主要 API 端點
```
GET    /api/v2/realtime/tags     # 獲取即時數據
GET    /api/v2/history/tags      # 獲取歷史數據
GET    /api/v2/devices           # 獲取設備列表
GET    /api/v2/alarms            # 獲取告警列表
POST   /api/v2/auth/login        # 用戶登入
```

## 🎨 UI 重構計劃

### 設計系統
- 📖 [UI 設計指南](./docs/UI-GUIDE.md)
- 🎨 [色彩系統](./docs/UI-GUIDE.md#色彩系統)
- 📝 [字體系統](./docs/UI-GUIDE.md#字體系統)
- 📏 [間距系統](./docs/UI-GUIDE.md#間距系統)

### 重構進度
- [ ] 基礎設施建立（Naive UI + Tailwind CSS）
- [ ] 核心元件開發（BaseCard, BaseButton, BaseTable 等）
- [ ] 主要頁面重構（儀表板、數據表格、圖表頁面）
- [ ] 響應式設計與主題系統
- [ ] 測試與優化

## 🤝 貢獻指南

### 如何貢獻
1. Fork 本專案
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交變更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

### 代碼審查
- 所有 PR 都需要經過代碼審查
- 確保測試通過
- 遵循代碼規範
- 更新相關文檔

### 問題回報
- 使用 GitHub Issues 回報問題
- 提供詳細的問題描述
- 包含重現步驟
- 附上相關截圖或日誌

## 📄 相關文檔

- [專案規則文檔](./docs/project-rules.md)
- [技術前提約束](./docs/TechnicalPrerequisites.md)
- [技術限制約束](./docs/TechnicalConstraints.md)
- [環境變數設定規範](./docs/環境變數設定規範.md)

## 📞 聯絡資訊

- **專案負責人**: PLC 開發團隊
- **Email**: <EMAIL>
- **GitHub**: https://github.com/ococomtw/plc-frontend
- **官網**: https://www.ococom.tw

## 📜 授權條款

本專案採用 MIT 授權條款 - 詳見 [LICENSE](LICENSE) 文件

---

**⚠️ 重要提醒**
- 開發前請務必閱讀 [專案規則文檔](./docs/project-rules.md)
- 遵循 [技術約束條件](./docs/TechnicalConstraints.md)
- UI 變更需參考 [UI 設計指南](./docs/UI-GUIDE.md)