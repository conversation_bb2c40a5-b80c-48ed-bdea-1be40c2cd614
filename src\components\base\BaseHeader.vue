<template>
  <header :class="headerClasses">
    <div class="header-container">
      <!-- 左側區域 -->
      <div class="header-left">
        <!-- Logo 和標題 -->
        <div v-if="$slots.logo || logo || title" class="flex items-center space-x-3">
          <div v-if="logo" class="flex-shrink-0">
            <img :src="logo" :alt="title" class="w-8 h-8" />
          </div>
          <div v-if="title && !hideTitleOnMobile" class="hidden sm:block">
            <h1 class="text-xl font-semibold text-gray-900">{{ title }}</h1>
            <p v-if="subtitle" class="text-sm text-gray-500">{{ subtitle }}</p>
          </div>
          <slot name="logo" />
        </div>
        
        <!-- 側邊欄切換按鈕 -->
        <button
          v-if="showSidebarToggle"
          @click="toggleSidebar"
          class="sidebar-toggle-btn"
        >
          <n-icon :size="20">
            <component :is="MenuIcon" />
          </n-icon>
        </button>
        
        <!-- 左側自定義內容 -->
        <div v-if="$slots.left" class="flex items-center space-x-3">
          <slot name="left" />
        </div>
      </div>
      
      <!-- 中間區域 -->
      <div v-if="$slots.center" class="header-center">
        <slot name="center" />
      </div>
      
      <!-- 右側區域 -->
      <div class="header-right">
        <!-- 搜尋框 -->
        <div v-if="showSearch" class="search-container">
          <n-input
            v-model:value="searchValue"
            :placeholder="searchPlaceholder"
            clearable
            @input="handleSearch"
            @keyup.enter="handleSearchEnter"
          >
            <template #prefix>
              <n-icon :size="16">
                <component :is="SearchIcon" />
              </n-icon>
            </template>
          </n-input>
        </div>
        
        <!-- 通知 -->
        <div v-if="showNotifications" class="notification-container">
          <n-popover trigger="click" placement="bottom-end">
            <template #trigger>
              <button class="notification-btn">
                <n-badge :value="notificationCount" :max="99" show-zero>
                  <n-icon :size="20">
                    <component :is="NotificationsIcon" />
                  </n-icon>
                </n-badge>
              </button>
            </template>
            <div class="notification-panel">
              <div class="notification-header">
                <h3 class="text-sm font-semibold text-gray-900">通知</h3>
                <button v-if="notificationCount > 0" @click="clearAllNotifications" class="text-xs text-primary-600 hover:text-primary-700">
                  全部清除
                </button>
              </div>
              <div class="notification-list">
                <div v-if="notifications.length === 0" class="notification-empty">
                  <p class="text-sm text-gray-500">暫無通知</p>
                </div>
                <div
                  v-for="notification in notifications"
                  :key="notification.id"
                  class="notification-item"
                  @click="handleNotificationClick(notification)"
                >
                  <div class="flex items-start space-x-3">
                    <div class="notification-icon">
                      <n-icon :size="16" :color="getNotificationColor(notification.type)">
                        <component :is="getNotificationIcon(notification.type)" />
                      </n-icon>
                    </div>
                    <div class="flex-1">
                      <p class="text-sm font-medium text-gray-900">{{ notification.title }}</p>
                      <p class="text-xs text-gray-500">{{ notification.message }}</p>
                      <p class="text-xs text-gray-400 mt-1">{{ formatTime(notification.time) }}</p>
                    </div>
                    <button
                      @click.stop="removeNotification(notification.id)"
                      class="notification-close"
                    >
                      <n-icon :size="14">
                        <component :is="CloseIcon" />
                      </n-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </n-popover>
        </div>
        
        <!-- 用戶選單 -->
        <div v-if="showUserMenu" class="user-menu-container">
          <n-dropdown
            :options="userMenuOptions"
            @select="handleUserMenuSelect"
            placement="bottom-end"
          >
            <button class="user-menu-btn">
              <div class="flex items-center space-x-2">
                <n-avatar
                  :size="32"
                  :src="userAvatar"
                  :fallback-src="defaultAvatar"
                  round
                >
                  {{ userInitials }}
                </n-avatar>
                <div v-if="showUserInfo" class="hidden sm:block text-left">
                  <p class="text-sm font-medium text-gray-900">{{ userName }}</p>
                  <p class="text-xs text-gray-500">{{ userRole }}</p>
                </div>
                <n-icon :size="16" class="text-gray-400">
                  <component :is="ChevronDownIcon" />
                </n-icon>
              </div>
            </button>
          </n-dropdown>
        </div>
        
        <!-- 主題切換 -->
        <div v-if="showThemeToggle" class="theme-toggle-container">
          <button @click="toggleTheme" class="theme-toggle-btn">
            <n-icon :size="20">
              <component :is="isDark ? SunIcon : MoonIcon" />
            </n-icon>
          </button>
        </div>
        
        <!-- 右側自定義內容 -->
        <div v-if="$slots.right" class="flex items-center space-x-3">
          <slot name="right" />
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { computed, ref } from 'vue'
import { 
  NIcon, 
  NInput, 
  NBadge, 
  NPopover, 
  NDropdown, 
  NAvatar 
} from 'naive-ui'
import {
  MenuOutline as MenuIcon,
  SearchOutline as SearchIcon,
  NotificationsOutline as NotificationsIcon,
  CloseOutline as CloseIcon,
  ChevronDownOutline as ChevronDownIcon,
  SunnyOutline as SunIcon,
  MoonOutline as MoonIcon,
  InformationCircleOutline as InfoIcon,
  CheckmarkCircleOutline as SuccessIcon,
  WarningOutline as WarningIcon,
  CloseCircleOutline as ErrorIcon
} from '@vicons/ionicons5'

export default {
  name: 'BaseHeader',
  components: {
    NIcon,
    NInput,
    NBadge,
    NPopover,
    NDropdown,
    NAvatar
  },
  props: {
    // 基本屬性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    logo: {
      type: String,
      default: ''
    },
    
    // 樣式
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'secondary', 'transparent'].includes(value)
    },
    fixed: {
      type: Boolean,
      default: false
    },
    shadow: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: true
    },
    
    // 功能開關
    showSidebarToggle: {
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: false
    },
    showNotifications: {
      type: Boolean,
      default: false
    },
    showUserMenu: {
      type: Boolean,
      default: false
    },
    showThemeToggle: {
      type: Boolean,
      default: false
    },
    
    // 搜尋
    searchPlaceholder: {
      type: String,
      default: '搜尋...'
    },
    
    // 通知
    notifications: {
      type: Array,
      default: () => []
    },
    
    // 用戶資訊
    userName: {
      type: String,
      default: ''
    },
    userRole: {
      type: String,
      default: ''
    },
    userAvatar: {
      type: String,
      default: ''
    },
    showUserInfo: {
      type: Boolean,
      default: true
    },
    userMenuOptions: {
      type: Array,
      default: () => [
        { label: '個人資料', key: 'profile' },
        { label: '設定', key: 'settings' },
        { type: 'divider' },
        { label: '登出', key: 'logout' }
      ]
    },
    
    // 響應式
    hideTitleOnMobile: {
      type: Boolean,
      default: false
    },
    
    // 主題
    isDark: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'sidebar-toggle',
    'search',
    'search-enter',
    'notification-click',
    'notification-remove',
    'notifications-clear',
    'user-menu-select',
    'theme-toggle'
  ],
  setup(props, { emit }) {
    const searchValue = ref('')
    const defaultAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNGM0Y0RjYiLz4KPHBhdGggZD0iTTE2IDhDMTMuNzkgOCAxMiA5Ljc5IDEyIDEyQzEyIDE0LjIxIDEzLjc5IDE2IDE2IDE2QzE4LjIxIDE2IDIwIDE0LjIxIDIwIDEyQzIwIDkuNzkgMTguMjEgOCAxNiA4WiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTYgMThDMTIuNjkgMTggMTAgMjAuNjkgMTAgMjRIMjJDMjIgMjAuNjkgMTkuMzEgMTggMTYgMThaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo='
    
    // 計算標題樣式類別
    const headerClasses = computed(() => {
      const classes = ['base-header']
      
      if (props.fixed) {
        classes.push('fixed', 'top-0', 'left-0', 'right-0', 'z-50')
      }
      
      if (props.shadow) {
        classes.push('shadow-sm')
      }
      
      if (props.bordered) {
        classes.push('border-b', 'border-gray-200')
      }
      
      // 變體樣式
      switch (props.variant) {
        case 'primary':
          classes.push('bg-primary-600', 'text-white')
          break
        case 'secondary':
          classes.push('bg-secondary-600', 'text-white')
          break
        case 'transparent':
          classes.push('bg-transparent')
          break
        default:
          classes.push('bg-white', 'text-gray-900')
      }
      
      return classes
    })
    
    // 計算通知數量
    const notificationCount = computed(() => {
      return props.notifications.filter(n => !n.read).length
    })
    
    // 計算用戶姓名縮寫
    const userInitials = computed(() => {
      if (!props.userName) return ''
      return props.userName
        .split(' ')
        .map(name => name.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2)
    })
    
    // 切換側邊欄
    const toggleSidebar = () => {
      emit('sidebar-toggle')
    }
    
    // 處理搜尋
    const handleSearch = (value) => {
      emit('search', value)
    }
    
    const handleSearchEnter = () => {
      emit('search-enter', searchValue.value)
    }
    
    // 處理通知
    const handleNotificationClick = (notification) => {
      emit('notification-click', notification)
    }
    
    const removeNotification = (id) => {
      emit('notification-remove', id)
    }
    
    const clearAllNotifications = () => {
      emit('notifications-clear')
    }
    
    // 獲取通知圖標
    const getNotificationIcon = (type) => {
      switch (type) {
        case 'success':
          return SuccessIcon
        case 'warning':
          return WarningIcon
        case 'error':
          return ErrorIcon
        default:
          return InfoIcon
      }
    }
    
    // 獲取通知顏色
    const getNotificationColor = (type) => {
      switch (type) {
        case 'success':
          return '#10b981'
        case 'warning':
          return '#f59e0b'
        case 'error':
          return '#ef4444'
        default:
          return '#3b82f6'
      }
    }
    
    // 格式化時間
    const formatTime = (time) => {
      const now = new Date()
      const notificationTime = new Date(time)
      const diff = now - notificationTime
      
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 1) return '剛剛'
      if (minutes < 60) return `${minutes} 分鐘前`
      if (hours < 24) return `${hours} 小時前`
      return `${days} 天前`
    }
    
    // 處理用戶選單
    const handleUserMenuSelect = (key) => {
      emit('user-menu-select', key)
    }
    
    // 切換主題
    const toggleTheme = () => {
      emit('theme-toggle')
    }
    
    return {
      searchValue,
      headerClasses,
      notificationCount,
      userInitials,
      defaultAvatar,
      toggleSidebar,
      handleSearch,
      handleSearchEnter,
      handleNotificationClick,
      removeNotification,
      clearAllNotifications,
      getNotificationIcon,
      getNotificationColor,
      formatTime,
      handleUserMenuSelect,
      toggleTheme,
      MenuIcon,
      SearchIcon,
      NotificationsIcon,
      CloseIcon,
      ChevronDownIcon,
      SunIcon,
      MoonIcon
    }
  }
}
</script>

<style scoped>
.base-header {
  @apply w-full;
}

.header-container {
  @apply flex items-center justify-between px-4 py-3;
}

.header-left {
  @apply flex items-center space-x-4;
}

.header-center {
  @apply flex-1 flex items-center justify-center mx-8;
}

.header-right {
  @apply flex items-center space-x-4;
}

.sidebar-toggle-btn {
  @apply p-2 rounded-lg transition-colors duration-200 hover:bg-gray-100;
}

.search-container {
  @apply w-64;
}

.notification-container {
  @apply relative;
}

.notification-btn {
  @apply p-2 rounded-lg transition-colors duration-200 hover:bg-gray-100;
}

.notification-panel {
  @apply w-80 max-h-96 overflow-hidden;
}

.notification-header {
  @apply flex items-center justify-between p-3 border-b border-gray-200;
}

.notification-list {
  @apply max-h-64 overflow-y-auto;
}

.notification-empty {
  @apply p-4 text-center;
}

.notification-item {
  @apply p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors duration-200;
}

.notification-icon {
  @apply flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center;
}

.notification-close {
  @apply p-1 rounded-full hover:bg-gray-200 transition-colors duration-200;
}

.user-menu-container {
  @apply relative;
}

.user-menu-btn {
  @apply p-2 rounded-lg transition-colors duration-200 hover:bg-gray-100;
}

.theme-toggle-container {
  @apply relative;
}

.theme-toggle-btn {
  @apply p-2 rounded-lg transition-colors duration-200 hover:bg-gray-100;
}

/* 深色模式支援 */
.dark .sidebar-toggle-btn:hover,
.dark .notification-btn:hover,
.dark .user-menu-btn:hover,
.dark .theme-toggle-btn:hover {
  @apply bg-gray-700;
}

.dark .notification-header {
  @apply border-gray-700;
}

.dark .notification-item {
  @apply border-gray-700 hover:bg-gray-700;
}

.dark .notification-close:hover {
  @apply bg-gray-600;
}

/* 響應式設計 */
@media (max-width: 640px) {
  .search-container {
    @apply w-48;
  }
  
  .header-center {
    @apply mx-4;
  }
}
</style>
