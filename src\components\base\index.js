// 基礎元件統一導出
import BaseCard from './BaseCard.vue'
import BaseButton from './BaseButton.vue'
import BaseTable from './BaseTable.vue'
import BaseChart from './BaseChart.vue'
import BaseSidebar from './BaseSidebar.vue'
import BaseHeader from './BaseHeader.vue'

// 全域註冊基礎元件的函數
export function registerBaseComponents(app) {
  app.component('BaseCard', BaseCard)
  app.component('BaseButton', BaseButton)
  app.component('BaseTable', BaseTable)
  app.component('BaseChart', BaseChart)
  app.component('BaseSidebar', BaseSidebar)
  app.component('BaseHeader', BaseHeader)
}

// 個別導出
export {
  BaseCard,
  BaseButton,
  BaseTable,
  BaseChart,
  BaseSidebar,
  BaseHeader
}

// 預設導出
export default {
  BaseCard,
  BaseButton,
  BaseTable,
  install: registerBaseComponents
}
