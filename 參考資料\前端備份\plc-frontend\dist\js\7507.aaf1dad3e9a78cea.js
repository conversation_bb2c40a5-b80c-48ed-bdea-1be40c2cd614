"use strict";(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[3003,7507],{45118:function(e,a,t){t.d(a,{A:function(){return v}});var l=t(20641),n=t(72644);const o={class:"ninjadash-datatable-filter"},r={key:0,class:"ninjadash-datatable-filter__right"},i={class:"ninjadasj-datatable"};function d(e,a,t,d,u,s){const c=(0,l.g2)("sdButton"),p=(0,l.g2)("a-space"),m=(0,l.g2)("unicon"),g=(0,l.g2)("a-input"),f=(0,l.g2)("a-button"),h=(0,l.g2)("a-col"),v=(0,l.g2)("a-row"),b=(0,l.g2)("a-table"),C=(0,l.g2)("TableWrapper"),y=(0,l.g2)("DataTableStyleWrap");return(0,l.uX)(),(0,l.Wv)(y,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",o,[(0,l.Lk)("div",null,[(0,l.bF)(p,null,{default:(0,l.k6)((()=>[e.addOption?((0,l.uX)(),(0,l.Wv)(c,{key:0,class:"act-btn",type:"primary",onClick:e.handleAdd},{default:(0,l.k6)((()=>[(0,l.eW)(" 新增 ")])),_:1},8,["onClick"])):(0,l.Q3)("",!0),e.backOption?((0,l.uX)(),(0,l.Wv)(c,{key:1,size:"default",outlined:!0,type:"primary",onClick:e.handleBack},{default:(0,l.k6)((()=>[(0,l.eW)(" 回上層 "+(0,n.v_)(e.backTitle),1)])),_:1},8,["onClick"])):(0,l.Q3)("",!0)])),_:1})]),e.filterOption?((0,l.uX)(),(0,l.CE)("div",r,[(0,l.bF)(g,{onChange:e.handleDataSearch,size:"default",placeholder:"搜尋"},{prefix:(0,l.k6)((()=>[(0,l.bF)(m,{name:"search"})])),_:1},8,["onChange"])])):(0,l.Q3)("",!0)]),(0,l.bF)(v,{align:"end"},{default:(0,l.k6)((()=>[e.importOption?((0,l.uX)(),(0,l.Wv)(h,{key:0,style:{"margin-bottom":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(f,{type:"primary",ghost:"",onClick:e.handleImport},{default:(0,l.k6)((()=>[(0,l.eW)("匯入")])),_:1},8,["onClick"])])),_:1})):(0,l.Q3)("",!0),e.exportOption?((0,l.uX)(),(0,l.Wv)(h,{key:1,style:{"margin-bottom":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(f,{type:"primary",ghost:"",onClick:e.handleExport},{default:(0,l.k6)((()=>[(0,l.eW)("匯出Excel")])),_:1},8,["onClick"])])),_:1})):(0,l.Q3)("",!0)])),_:1}),(0,l.Lk)("div",i,[(0,l.bF)(C,{class:"table-data-view table-responsive"},{default:(0,l.k6)((()=>[e.rowSelection?((0,l.uX)(),(0,l.Wv)(b,{key:0,class:"ant-table-striped","row-selection":e.rowSelections,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},childrenColumnName:e.childrenColumnName,"row-class-name":e.getRowClassName,"data-source":e.tableData,columns:e.columns,onChange:e.changePageSize},null,8,["row-selection","pagination","childrenColumnName","row-class-name","data-source","columns","onChange"])):((0,l.uX)(),(0,l.Wv)(b,{key:1,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},class:"ant-table-striped",childrenColumnName:e.childrenColumnName,"data-source":e.tableData,"row-class-name":e.getRowClassName,columns:e.columns,onChange:e.changePageSize},null,8,["pagination","childrenColumnName","data-source","row-class-name","columns","onChange"]))])),_:1})])])),_:1})}var u=t(79841),s=t(19732),c=t(95853);const p=c.Ay.div`
    .ninjadash-datatable-filter{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin: 20px 0 25px 0;
        @media only screen and (max-width: 575px){
            flex-wrap: wrap;
        }
        .ninjadash-datatable-filter__left{
            display: inline-flex;
            width: 100%;
            align-items: center;
            .ant-form{
                display: inline-flex;
                width: 100%;
                align-items: center;
            }
            span.label{
                margin-right: 8px;
                color: ${({theme:e})=>e[e.mainContent]["gray-text"]};
            }
            .ninjadash-datatable-filter__input{
                display: flex;
                align-items: center;
                padding-right: 20px;
                .ant-input{
                    height: 40px;
                }
            }
        }
        .ninjadash-datatable-filter__right{
            min-width: 280px;
            @media only screen and (max-width: 575px){
                margin-top: 15px;
            }
            .ant-input-affix-wrapper{
                padding: 7.22px 20px;
                border-radius: 6px;
                .ant-input-prefix{
                    svg{
                        width: 16px;
                        height: 16px;
                        fill: ${({theme:e})=>e[e.mainContent]["light-text"]};
                    }
                }
            }
        }
    }
`;var m=t(79570),g=(0,l.pM)({components:{DataTableStyleWrap:p,TableWrapper:m.AC},props:{filterOption:s.Ay.bool,filterOnchange:s.Ay.bool,rowSelection:s.Ay.bool,defaultSelected:s.Ay.array,tableData:s.Ay.array,columns:s.Ay.array,handleDataSearch:s.Ay.func,handleAdd:s.Ay.func,handleBack:s.Ay.func,handleImport:s.Ay.func,handleExport:s.Ay.func,rowClassFunc:{type:Function,default:()=>{}},backOption:{type:Boolean,default:!1},addOption:{type:Boolean,default:!1},exportOption:{type:Boolean,default:!1},importOption:{type:Boolean,default:!1},expandedRow:{type:Object,default:null},childrenColumnName:{type:String,default:"children"},backTitle:{type:String,default:""}},setup(e,{emit:a}){const t=(0,u.KR)([]);(0,l.wB)((()=>e.defaultSelected),(e=>{t.value=e}),{immediate:!0});const n=e=>{t.value=e,a("onSelectChange",t.value)},o=(0,l.EW)((()=>({selectedRowKeys:(0,u.R1)(t),onChange:n,hideDefaultSelections:!0}))),r=(0,u.KR)(10),i=(0,u.KR)(["10","20","50","100"]),d=e=>{r.value=e.pageSize},s=({record:a})=>e.expandedRow.innerDataProp?a[e.expandedRow.innerDataProp]:a.children,c=(a,t)=>e.rowClassFunc(a)??t%2===1?"table-striped row-style":"row-style";return{pageSize:r,pageSizeOptions:i,rowSelections:o,changePageSize:d,getInnerData:s,getRowClassName:c}}}),f=t(66262);const h=(0,f.A)(g,[["render",d],["__scopeId","data-v-1b881e36"]]);var v=h},75126:function(e,a,t){t(16859)},87507:function(e,a,t){t.r(a),t.d(a,{default:function(){return S}});var l=t(20641),n=t(9322);const o=(0,l.Lk)("p",null,"地區:",-1);function r(e,a,t,r,i,d){const u=(0,l.g2)("a-input"),s=(0,l.g2)("a-form-item"),c=(0,l.g2)("a-tree-select"),p=(0,l.g2)("a-spin"),m=(0,l.g2)("sdButton"),g=(0,l.g2)("a-col"),f=(0,l.g2)("a-row"),h=(0,l.g2)("a-form"),v=(0,l.g2)("sdModal"),b=(0,l.g2)("cctvStream"),C=(0,l.g2)("sdPageHeader"),y=(0,l.g2)("sdCards"),k=(0,l.g2)("DataTables"),S=(0,l.g2)("Main");return(0,l.uX)(),(0,l.CE)("div",null,[e.modal?((0,l.uX)(),(0,l.Wv)(v,{key:0,title:e.formState.title,visible:e.modal,onCancel:e.closeModal},{default:(0,l.k6)((()=>[(0,l.bF)(h,{model:e.formState,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,rules:e.rules,labelAlign:"left",onFinish:e.submitForm},{default:(0,l.k6)((()=>[(0,l.bF)(s,{label:"名稱",name:"name"},{default:(0,l.k6)((()=>[(0,l.bF)(u,{value:e.formState.name,"onUpdate:value":a[0]||(a[0]=a=>e.formState.name=a),placeholder:"名稱"},null,8,["value"])])),_:1}),(0,l.bF)(s,{label:"說明",name:"description"},{default:(0,l.k6)((()=>[(0,l.bF)(u,{value:e.formState.description,"onUpdate:value":a[1]||(a[1]=a=>e.formState.description=a),placeholder:"說明"},null,8,["value"])])),_:1}),(0,l.bF)(s,{label:"地區",name:"region"},{default:(0,l.k6)((()=>[(0,l.bF)(c,{value:e.formState.region,"onUpdate:value":a[2]||(a[2]=a=>e.formState.region=a),"tree-data":e.locations,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},"show-checked-strategy":e.SHOW_PARENT,placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data","show-checked-strategy"])])),_:1}),(0,l.bF)(s,{label:"串流網址",name:"streamUri"},{default:(0,l.k6)((()=>[(0,l.bF)(u,{value:e.formState.streamUri,"onUpdate:value":a[3]||(a[3]=a=>e.formState.streamUri=a)},null,8,["value"])])),_:1}),(0,l.bF)(s,{label:"使用者帳號",name:"username"},{default:(0,l.k6)((()=>[(0,l.bF)(u,{value:e.formState.username,"onUpdate:value":a[4]||(a[4]=a=>e.formState.username=a)},null,8,["value"])])),_:1}),(0,l.bF)(s,{label:"使用者密碼",name:"password"},{default:(0,l.k6)((()=>[(0,l.bF)(u,{value:e.formState.password,"onUpdate:value":a[5]||(a[5]=a=>e.formState.password=a)},null,8,["value"])])),_:1}),(0,l.bF)(s,{label:"製造商",name:"manufacturer"},{default:(0,l.k6)((()=>[(0,l.bF)(u,{value:e.formState.manufacturer,"onUpdate:value":a[6]||(a[6]=a=>e.formState.manufacturer=a)},null,8,["value"])])),_:1}),(0,l.bF)(s,{label:"型號",name:"model"},{default:(0,l.k6)((()=>[(0,l.bF)(u,{value:e.formState.model,"onUpdate:value":a[7]||(a[7]=a=>e.formState.model=a)},null,8,["value"])])),_:1}),(0,l.bF)(f,{align:"center",justify:"center",gutter:10},{default:(0,l.k6)((()=>[(0,l.bF)(g,null,{default:(0,l.k6)((()=>[(0,l.bF)(m,{class:"act-btn",type:"primary","html-type":"submit",disabled:e.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 儲存 "),(0,l.bo)((0,l.bF)(p,{size:"small"},null,512),[[n.aG,e.loading]])])),_:1},8,["disabled"])])),_:1}),(0,l.bF)(g,null,{default:(0,l.k6)((()=>[(0,l.bF)(m,{class:"act-btn","html-type":"submit",type:"light",onClick:(0,n.D$)(e.closeModal,["prevent"])},{default:(0,l.k6)((()=>[(0,l.eW)(" 取消 ")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["model","label-col","wrapper-col","rules","onFinish"])])),_:1},8,["title","visible","onCancel"])):(0,l.Q3)("",!0),e.CCTVModal?((0,l.uX)(),(0,l.Wv)(v,{key:1,title:`${e.currCCTVName}`,visible:e.CCTVModal,onCancel:e.closeCCTVModal},{default:(0,l.k6)((()=>[(0,l.bF)(b,{cctv:e.currCCTV},null,8,["cctv"])])),_:1},8,["title","visible","onCancel"])):(0,l.Q3)("",!0),(0,l.bF)(C,{title:"CCTV",class:"ninjadash-page-header-main",routes:[{breadcrumbName:"系統"},{breadcrumbName:"CCTV"}]}),(0,l.bF)(S,null,{default:(0,l.k6)((()=>[(0,l.bF)(y,{title:"條件篩選"},{default:(0,l.k6)((()=>[o,(0,l.bF)(c,{value:e.filterFormState.region,"onUpdate:value":a[8]||(a[8]=a=>e.filterFormState.region=a),style:{width:"300px"},"tree-data":e.locations,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},"show-checked-strategy":e.SHOW_PARENT,placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data","show-checked-strategy"])])),_:1}),(0,l.bF)(y,null,{default:(0,l.k6)((()=>[e.loading?((0,l.uX)(),(0,l.Wv)(p,{key:0})):(0,l.Q3)("",!0),e.init?((0,l.uX)(),(0,l.Wv)(k,{key:1,filterOption:!0,filterOnchange:!0,tableData:e.tableData,columns:e.columns,rowSelection:!1,addOption:e.permission.create,importOption:e.permission.create,handleImport:e.importCCTV,handleDataSearch:e.search,handleAdd:e.openAddModal},null,8,["tableData","columns","addOption","importOption","handleImport","handleDataSearch","handleAdd"])):(0,l.Q3)("",!0)])),_:1})])),_:1})])}t(75126);var i=t(56427),d=(t(1532),t(30995)),u=(t(6070),t(7179)),s=(t(18111),t(20116),t(61701),t(40834)),c=t(79570),p=t(79841),m=t(94734),g=t(45118),f=t(95853);f.Ay.div`
    color:red;
`;const h=f.Ay.div`
    display:flex;
    justify-content:end;
    svg{
       height:20px;
       cursor:pointer;
       margin:0 5px;
    }
`;var v=t(82958),b=t(47622),C=(0,l.pM)({components:{Main:c.gZ,LevelSelect:m.A,DataTables:g.A,cctvStream:b.A},setup(){const{permission:e}=(0,v.J)(),a=u.Ay.SHOW_PARENT,{dispatch:t,state:n}=(0,s.Pj)(),o=(0,l.EW)((()=>n.cctv.loading)),r=(0,p.KR)(!1);(0,l.sV)((async()=>{const e=await t("cctv/getAllCCTVAndOptions");c.value=e.locations,r.value=!0})),(0,l.xo)((()=>{t("cctv/resetCCTVState")}));const c=(0,p.KR)([]),m=(0,p.KR)(!1),g=(0,p.Kh)({region:null,text:null}),f=[{title:"名稱",dataIndex:"Name",key:"Name"},{title:"地區",dataIndex:"locationName",key:"locationName"},{title:"操作",dataIndex:"action",key:"action"}],b=()=>{const e=[{type:"text",target:g.text},{type:"list",target:g.region?g.region:null,source:"RegionList",sourceProp:"Id"}];t("cctv/filterCCTVTable",e)};(0,l.wB)((()=>g),b,{deep:!0}),(0,l.wB)((()=>n.cctv.initData),b,{deep:!0});const C=(0,l.EW)((()=>n.cctv.tableData.map((a=>({...a,locationName:a.RegionList.map((e=>e.Name)).join(" > "),action:(0,l.bF)(h,null,{default:()=>[(0,l.bF)("span",{onClick:()=>I(a)},[(0,l.bF)((0,l.g2)("unicon"),{name:"video"},null)]),e.update&&(0,l.bF)("span",{onClick:()=>N(a.Id)},[(0,l.bF)((0,l.g2)("unicon"),{name:"edit"},null)]),e.delete&&(0,l.bF)("span",{onClick:()=>W(a.Id)},[(0,l.bF)((0,l.g2)("unicon"),{name:"trash"},null)])]})}))))),y=e=>{g.text=e.target.value},k=(0,p.KR)(!1),S=(0,p.Kh)({title:"",id:null,name:"",description:"",username:"",password:"",manufacturer:"",model:"",region:null,streamUri:""}),w=(0,p.KR)(!1),x={lg:8,md:9,xs:24},F={lg:16,md:15,xs:24},_=()=>{d.A.confirm({title:"將從區網匯入所有攝影機",okText:"確認",cancelText:"取消",confirmLoading:o.value,onOk:async()=>{try{await t("cctv/importCCTV"),d.A.success({content:"已開始匯入，請稍等1分鐘後刷新"})}catch(e){d.A.error({title:"發生錯誤",content:e.message})}}})},A={name:[{required:!0,message:"請輸入名稱",trigger:"blur"}],description:[{required:!0,message:"請輸入說明",trigger:"blur"}],region:[{required:!0,message:"請選擇地區",trigger:"blur"}],streamUri:[{required:!0,message:"請輸入串流URL",trigger:"blur"}]},O=()=>{S.title="新增攝影機",S.id=null,S.name="",S.description="",S.username="",S.password="",S.manufacturer="",S.model="",S.region=null,S.streamUri="",k.value=!0},N=e=>{S.title="編輯攝影機";const a=C.value.find((a=>a.Id===e));S.id=a.Id,S.name=a.Name,S.description=a.Description,S.username=a.UserName,S.password=a.Password,S.manufacturer=a.Manufacturer,S.model=a.Model,S.region=a.RegionList[a.RegionList.length-1].Id,S.streamUri=a.ProfileUrl,k.value=!0},T=()=>{k.value=!1},W=e=>{d.A.confirm({title:"確認刪除?",okText:"確認",cancelText:"取消",confirmLoading:o.value,onOk:async()=>{try{await t("cctv/deleteCCTV",e),i.A.success({message:"刪除成功"})}catch(a){d.A.error({title:"發生錯誤",content:a.message})}}})},R=async()=>{try{let e;S.id?(await t("cctv/editCCTV",(0,p.ux)(S)),e="修改成功"):(await t("cctv/addCCTV",(0,p.ux)(S)),e="新增成功"),k.value=!1,i.A.success({message:e})}catch(e){d.A.error({title:"發生錯誤",content:e.message})}},D=(0,p.KR)(!1),V=(0,p.KR)([]),U=(0,p.KR)(""),I=({Id:e,Name:a})=>{V.value=[e],U.value=a,D.value=!0},z=()=>{D.value=!1};return{permission:e,SHOW_PARENT:a,loading:o,locations:c,filterFormState:g,searching:m,search:y,modal:k,formState:S,rules:A,init:r,importCCTV:_,columns:f,tableData:C,submitable:w,labelCol:x,wrapperCol:F,openAddModal:O,openEditModal:N,closeModal:T,submitForm:R,CCTVModal:D,currCCTV:V,currCCTVName:U,closeCCTVModal:z}}}),y=t(66262);const k=(0,y.A)(C,[["render",r]]);var S=k},94734:function(e,a,t){t.d(a,{A:function(){return p}});var l=t(20641),n=t(9322),o=t(72644);function r(e,a,t,r,i,d){const u=(0,l.g2)("a-select-option"),s=(0,l.g2)("a-select"),c=(0,l.g2)("a-space");return(0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(c,null,{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.levels,(t=>((0,l.uX)(),(0,l.Wv)(s,{value:e.selected[t],"onUpdate:value":a=>e.selected[t]=a,key:t,style:{width:"100%","min-width":"100px"},onChange:a=>e.handleChange(t)},{default:(0,l.k6)((()=>[e.nullOption?((0,l.uX)(),(0,l.Wv)(u,{key:0,value:null,onClick:a[0]||(a[0]=(0,n.D$)((()=>{}),["stop"]))},{default:(0,l.k6)((()=>[(0,l.eW)("無")])),_:1})):(0,l.Q3)("",!0),((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.getNextLevel(t),((t,r)=>((0,l.uX)(),(0,l.Wv)(u,{value:JSON.stringify(t),key:r,onClick:a[1]||(a[1]=(0,n.D$)((()=>{}),["stop"]))},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(t[e.childName]),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value","onChange"])))),128))])),_:1})])}t(44114);var i=t(79841),d=t(19732),u=(0,l.pM)({name:"LevelSelect",props:{selectedValue:{type:Object,default:null},group:d.Ay.array,nullOption:{type:Boolean,default:!1},childName:{type:String,default:"name"},childProp:{type:String,default:"children"}},setup(e,{emit:a}){const t=(0,i.KR)(e.selectedValue||{}),n=(0,i.KR)(e.selectedValue?Array.from(Array(Object.keys(e.selectedValue).length).keys()):[0]),o=(0,l.EW)((()=>e.group?e.group:[])),r=a=>{if(0===a)return o.value;const l=n.value[a-1],r=JSON.parse(t.value[l]);return r&&r[e.childProp]&&r[e.childProp].length>0?r[e.childProp]:[]},d=l=>{const o=n.value.indexOf(l);for(let e=o+1;e<n.value.length;e++)delete t.value[e];if(n.value.splice(o+1,n.value.length-(o+1)),t.value[l]){a("change",JSON.parse(t.value[n.value.length-1]),t.value);const o=JSON.parse(t.value[l]);o[e.childProp]&&n.value.push(l+1)}else{const e=n.value.length>1?t.value[n.value.length-2]:null;a("change",JSON.parse(e),t.value)}};return{selected:t,levels:n,getNextLevel:r,handleChange:d}}}),s=t(66262);const c=(0,s.A)(u,[["render",r]]);var p=c}}]);