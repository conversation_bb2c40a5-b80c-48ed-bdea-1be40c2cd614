/* Tailwind CSS 基礎樣式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS 自定義屬性 - 與設計系統同步 */
:root {
  /* 主要色彩 */
  --color-primary: #f97316;
  --color-primary-50: #fff7ed;
  --color-primary-100: #ffedd5;
  --color-primary-200: #fed7aa;
  --color-primary-300: #fdba74;
  --color-primary-400: #fb923c;
  --color-primary-500: #f97316;
  --color-primary-600: #ea580c;
  --color-primary-700: #c2410c;
  --color-primary-800: #9a3412;
  --color-primary-900: #7c2d12;

  /* 次要色彩 */
  --color-secondary: #1e293b;
  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  /* 功能色彩 */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* 中性色彩 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 字體系統 */
  --font-family-sans: 'Noto Sans TC', 'Inter', 'Roboto', system-ui, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;

  /* 字體大小 */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* 間距系統 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;

  /* 圓角系統 */
  --border-radius-sm: 0.125rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-3xl: 1.5rem;

  /* 陰影系統 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 動畫時間 */
  --transition-duration-fast: 150ms;
  --transition-duration-normal: 200ms;
  --transition-duration-slow: 300ms;

  /* Z-index 層級 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

/* 深色模式變數 */
.dark {
  --color-primary: #fb923c;
  --color-secondary: #f1f5f9;
  --color-gray-50: #111827;
  --color-gray-100: #1f2937;
  --color-gray-200: #374151;
  --color-gray-300: #4b5563;
  --color-gray-400: #6b7280;
  --color-gray-500: #9ca3af;
  --color-gray-600: #d1d5db;
  --color-gray-700: #e5e7eb;
  --color-gray-800: #f3f4f6;
  --color-gray-900: #f9fafb;
}

/* 基礎樣式重置 */
@layer base {
  * {
    @apply border-gray-200;
  }
  
  html {
    @apply antialiased;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  /* 標題樣式 */
  h1 {
    @apply text-4xl font-bold text-gray-900 leading-tight;
  }
  
  h2 {
    @apply text-3xl font-semibold text-gray-900 leading-tight;
  }
  
  h3 {
    @apply text-2xl font-semibold text-gray-900 leading-snug;
  }
  
  h4 {
    @apply text-xl font-medium text-gray-900 leading-snug;
  }
  
  h5 {
    @apply text-lg font-medium text-gray-900 leading-normal;
  }
  
  h6 {
    @apply text-base font-medium text-gray-900 leading-normal;
  }
  
  /* 連結樣式 */
  a {
    @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;
  }
  
  /* 表單元素樣式 */
  input, textarea, select {
    @apply border-gray-300 rounded-lg focus:border-primary-500 focus:ring-primary-500;
  }
  
  /* 按鈕基礎樣式 */
  button {
    @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
}

/* 組件樣式 */
@layer components {
  /* 卡片組件 */
  .card {
    @apply bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-100 bg-gray-50;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-100 bg-gray-50;
  }
  
  /* 按鈕組件 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-500;
  }
  
  .btn-error {
    @apply bg-error-500 text-white hover:bg-error-600 focus:ring-error-500;
  }
  
  .btn-outline {
    @apply border-2 bg-transparent hover:bg-gray-50;
  }
  
  .btn-outline-primary {
    @apply border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500;
  }
  
  /* 表格組件 */
  .table {
    @apply w-full border-collapse bg-white rounded-2xl overflow-hidden shadow-lg;
  }
  
  .table th {
    @apply px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200;
  }
  
  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-100;
  }
  
  .table tbody tr:hover {
    @apply bg-gray-50;
  }
  
  /* 表單組件 */
  .form-group {
    @apply mb-6;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:border-primary-500 focus:ring-primary-500 focus:ring-1;
  }
  
  .form-error {
    @apply mt-1 text-sm text-error-600;
  }
  
  /* 導航組件 */
  .nav {
    @apply flex space-x-8;
  }
  
  .nav-link {
    @apply text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }
  
  .nav-link.active {
    @apply text-primary-600 bg-primary-50;
  }
  
  /* 側邊欄組件 */
  .sidebar {
    @apply w-64 bg-white shadow-lg border-r border-gray-200 h-full;
  }
  
  .sidebar-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .sidebar-nav {
    @apply px-3 py-4;
  }
  
  .sidebar-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200;
  }
  
  .sidebar-nav-item.active {
    @apply text-primary-600 bg-primary-50;
  }
}

/* 工具樣式 */
@layer utilities {
  /* 文字截斷 */
  .text-truncate {
    @apply truncate;
  }
  
  /* 滾動條樣式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }
  
  /* 動畫工具 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
}

/* 動畫定義 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 響應式工具 */
@media (max-width: 640px) {
  .mobile-hidden {
    @apply hidden;
  }
  
  .mobile-full {
    @apply w-full;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-hidden {
    @apply hidden;
  }
}

@media (min-width: 1025px) {
  .desktop-hidden {
    @apply hidden;
  }
}
