{"version": 2, "dgSpecHash": "WxYeWRQjWcs=", "success": false, "projectFilePath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.WinFrmTool\\Oco.WinFrmTool.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1803", "level": "Warning", "warningLevel": 1, "message": "You are running the 'restore' operation with an 'HTTP' source, 'http://192.168.1.154:14235/v3/index.json'. Non-HTTPS access will be removed in a future version. Consider migrating to an 'HTTPS' source."}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Microsoft.EntityFrameworkCore.Tools"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Serilog"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "System.ServiceModel.Primitives"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "<PERSON><PERSON><PERSON>"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "System.Linq.Async"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Microsoft.EntityFrameworkCore.SqlServer"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "FastEnum"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Oco.ServiceUtility"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Oco.FeeCalculator.Water"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "ClosedXML"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Oco.FeeCalculator.Electricity"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "MQTTnet"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Microsoft.Extensions.DependencyInjection.Abstractions"}]}