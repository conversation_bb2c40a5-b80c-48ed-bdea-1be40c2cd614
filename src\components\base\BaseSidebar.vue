<template>
  <div :class="sidebarClasses">
    <!-- 側邊欄標題 -->
    <div v-if="$slots.header || title" class="sidebar-header">
      <div class="flex items-center space-x-3">
        <div v-if="logo" class="flex-shrink-0">
          <img :src="logo" :alt="title" class="w-8 h-8" />
        </div>
        <div v-if="!collapsed">
          <h2 v-if="title" class="text-lg font-semibold text-gray-900">{{ title }}</h2>
          <p v-if="subtitle" class="text-sm text-gray-500">{{ subtitle }}</p>
        </div>
        <slot name="header" />
      </div>
      
      <!-- 收合按鈕 -->
      <button
        v-if="collapsible"
        @click="toggleCollapse"
        class="sidebar-toggle-btn"
      >
        <n-icon :size="16">
          <component :is="collapsed ? ChevronRightIcon : ChevronLeftIcon" />
        </n-icon>
      </button>
    </div>
    
    <!-- 側邊欄內容 -->
    <div class="sidebar-content">
      <!-- 導航選單 -->
      <nav v-if="menuItems.length" class="sidebar-nav">
        <div class="space-y-1">
          <template v-for="item in menuItems" :key="item.key">
            <!-- 單層選單項目 -->
            <div v-if="!item.children || !item.children.length">
              <router-link
                :to="item.to || '#'"
                :class="getMenuItemClasses(item)"
                @click="handleMenuClick(item)"
              >
                <div class="flex items-center space-x-3">
                  <n-icon v-if="item.icon" :size="18">
                    <component :is="item.icon" />
                  </n-icon>
                  <span v-if="!collapsed" class="menu-text">{{ item.label }}</span>
                </div>
                <n-badge v-if="item.badge && !collapsed" :value="item.badge" />
              </router-link>
            </div>
            
            <!-- 多層選單項目 -->
            <div v-else>
              <div
                :class="getSubmenuHeaderClasses(item)"
                @click="toggleSubmenu(item.key)"
              >
                <div class="flex items-center space-x-3">
                  <n-icon v-if="item.icon" :size="18">
                    <component :is="item.icon" />
                  </n-icon>
                  <span v-if="!collapsed" class="menu-text">{{ item.label }}</span>
                </div>
                <n-icon v-if="!collapsed" :size="14" class="submenu-arrow">
                  <component :is="openSubmenus.includes(item.key) ? ChevronDownIcon : ChevronRightIcon" />
                </n-icon>
              </div>
              
              <!-- 子選單 -->
              <div v-if="!collapsed && openSubmenus.includes(item.key)" class="submenu">
                <router-link
                  v-for="child in item.children"
                  :key="child.key"
                  :to="child.to || '#'"
                  :class="getSubmenuItemClasses(child)"
                  @click="handleMenuClick(child)"
                >
                  <div class="flex items-center space-x-3">
                    <n-icon v-if="child.icon" :size="16">
                      <component :is="child.icon" />
                    </n-icon>
                    <span class="menu-text">{{ child.label }}</span>
                  </div>
                  <n-badge v-if="child.badge" :value="child.badge" />
                </router-link>
              </div>
            </div>
          </template>
        </div>
      </nav>
      
      <!-- 自定義內容插槽 -->
      <div v-if="$slots.default" class="sidebar-custom-content">
        <slot />
      </div>
    </div>
    
    <!-- 側邊欄底部 -->
    <div v-if="$slots.footer" class="sidebar-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script>
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { NIcon, NBadge } from 'naive-ui'
import { 
  ChevronLeftOutline as ChevronLeftIcon,
  ChevronRightOutline as ChevronRightIcon,
  ChevronDownOutline as ChevronDownIcon
} from '@vicons/ionicons5'

export default {
  name: 'BaseSidebar',
  components: {
    NIcon,
    NBadge
  },
  props: {
    // 基本屬性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    logo: {
      type: String,
      default: ''
    },
    
    // 選單項目
    menuItems: {
      type: Array,
      default: () => []
    },
    
    // 收合功能
    collapsible: {
      type: Boolean,
      default: true
    },
    collapsed: {
      type: Boolean,
      default: false
    },
    
    // 樣式
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'secondary', 'dark'].includes(value)
    },
    width: {
      type: String,
      default: '256px'
    },
    collapsedWidth: {
      type: String,
      default: '64px'
    },
    
    // 行為
    accordion: {
      type: Boolean,
      default: false
    },
    
    // 響應式
    responsive: {
      type: Boolean,
      default: true
    },
    mobileBreakpoint: {
      type: Number,
      default: 768
    }
  },
  emits: ['update:collapsed', 'menu-click', 'submenu-toggle'],
  setup(props, { emit }) {
    const route = useRoute()
    const openSubmenus = ref([])
    
    // 計算側邊欄樣式類別
    const sidebarClasses = computed(() => {
      const classes = [
        'base-sidebar',
        'flex',
        'flex-col',
        'h-full',
        'transition-all',
        'duration-300',
        'ease-in-out'
      ]
      
      // 寬度
      if (props.collapsed) {
        classes.push('sidebar-collapsed')
      } else {
        classes.push('sidebar-expanded')
      }
      
      // 變體樣式
      switch (props.variant) {
        case 'primary':
          classes.push('bg-primary-600', 'text-white')
          break
        case 'secondary':
          classes.push('bg-secondary-600', 'text-white')
          break
        case 'dark':
          classes.push('bg-gray-900', 'text-white')
          break
        default:
          classes.push('bg-white', 'text-gray-900', 'border-r', 'border-gray-200')
      }
      
      return classes
    })
    
    // 獲取選單項目樣式
    const getMenuItemClasses = (item) => {
      const classes = [
        'flex',
        'items-center',
        'justify-between',
        'px-3',
        'py-2',
        'rounded-lg',
        'transition-colors',
        'duration-200',
        'cursor-pointer'
      ]
      
      // 活躍狀態
      if (isMenuItemActive(item)) {
        if (props.variant === 'default') {
          classes.push('bg-primary-50', 'text-primary-600', 'border-r-2', 'border-primary-600')
        } else {
          classes.push('bg-white', 'bg-opacity-20', 'text-white')
        }
      } else {
        if (props.variant === 'default') {
          classes.push('text-gray-700', 'hover:bg-gray-50', 'hover:text-gray-900')
        } else {
          classes.push('text-gray-300', 'hover:bg-white', 'hover:bg-opacity-10', 'hover:text-white')
        }
      }
      
      // 禁用狀態
      if (item.disabled) {
        classes.push('opacity-50', 'cursor-not-allowed')
      }
      
      return classes
    }
    
    // 獲取子選單標題樣式
    const getSubmenuHeaderClasses = () => {
      const classes = [
        'flex',
        'items-center',
        'justify-between',
        'px-3',
        'py-2',
        'rounded-lg',
        'transition-colors',
        'duration-200',
        'cursor-pointer'
      ]
      
      if (props.variant === 'default') {
        classes.push('text-gray-700', 'hover:bg-gray-50', 'hover:text-gray-900')
      } else {
        classes.push('text-gray-300', 'hover:bg-white', 'hover:bg-opacity-10', 'hover:text-white')
      }
      
      return classes
    }
    
    // 獲取子選單項目樣式
    const getSubmenuItemClasses = (item) => {
      const classes = [
        'flex',
        'items-center',
        'justify-between',
        'pl-9',
        'pr-3',
        'py-2',
        'rounded-lg',
        'transition-colors',
        'duration-200',
        'cursor-pointer'
      ]
      
      // 活躍狀態
      if (isMenuItemActive(item)) {
        if (props.variant === 'default') {
          classes.push('bg-primary-50', 'text-primary-600')
        } else {
          classes.push('bg-white', 'bg-opacity-20', 'text-white')
        }
      } else {
        if (props.variant === 'default') {
          classes.push('text-gray-600', 'hover:bg-gray-50', 'hover:text-gray-900')
        } else {
          classes.push('text-gray-400', 'hover:bg-white', 'hover:bg-opacity-10', 'hover:text-white')
        }
      }
      
      return classes
    }
    
    // 檢查選單項目是否活躍
    const isMenuItemActive = (item) => {
      if (!item.to) return false
      
      if (typeof item.to === 'string') {
        return route.path === item.to
      }
      
      if (typeof item.to === 'object') {
        return route.name === item.to.name
      }
      
      return false
    }
    
    // 切換收合狀態
    const toggleCollapse = () => {
      emit('update:collapsed', !props.collapsed)
    }
    
    // 切換子選單
    const toggleSubmenu = (key) => {
      const index = openSubmenus.value.indexOf(key)
      
      if (props.accordion) {
        // 手風琴模式：只能開啟一個子選單
        openSubmenus.value = index === -1 ? [key] : []
      } else {
        // 一般模式：可以開啟多個子選單
        if (index === -1) {
          openSubmenus.value.push(key)
        } else {
          openSubmenus.value.splice(index, 1)
        }
      }
      
      emit('submenu-toggle', { key, open: index === -1 })
    }
    
    // 處理選單點擊
    const handleMenuClick = (item) => {
      if (item.disabled) return
      
      emit('menu-click', item)
    }
    
    // 監聽收合狀態變化
    watch(() => props.collapsed, (newValue) => {
      if (newValue) {
        openSubmenus.value = []
      }
    })
    
    // 初始化展開的子選單
    const initOpenSubmenus = () => {
      props.menuItems.forEach(menuItem => {
        if (menuItem.children && menuItem.children.some(child => isMenuItemActive(child))) {
          if (!openSubmenus.value.includes(menuItem.key)) {
            openSubmenus.value.push(menuItem.key)
          }
        }
      })
    }
    
    // 監聽路由變化
    watch(() => route.path, () => {
      if (!props.collapsed) {
        initOpenSubmenus()
      }
    }, { immediate: true })
    
    return {
      sidebarClasses,
      openSubmenus,
      getMenuItemClasses,
      getSubmenuHeaderClasses,
      getSubmenuItemClasses,
      isMenuItemActive,
      toggleCollapse,
      toggleSubmenu,
      handleMenuClick,
      ChevronLeftIcon,
      ChevronRightIcon,
      ChevronDownIcon
    }
  }
}
</script>

<style scoped>
.base-sidebar {
  min-height: 100vh;
}

.sidebar-expanded {
  width: v-bind(width);
}

.sidebar-collapsed {
  width: v-bind(collapsedWidth);
}

.sidebar-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.sidebar-content {
  @apply flex-1 p-4 overflow-y-auto;
}

.sidebar-footer {
  @apply p-4 border-t border-gray-200;
}

.sidebar-toggle-btn {
  @apply p-1 rounded-lg transition-colors duration-200;
}

.sidebar-toggle-btn:hover {
  @apply bg-gray-100;
}

.sidebar-nav {
  @apply space-y-1;
}

.submenu {
  @apply mt-1 space-y-1;
}

.submenu-arrow {
  @apply transition-transform duration-200;
}

.menu-text {
  @apply truncate;
}

/* 深色模式支援 */
.dark .sidebar-header {
  @apply border-gray-700;
}

.dark .sidebar-footer {
  @apply border-gray-700;
}

.dark .sidebar-toggle-btn:hover {
  @apply bg-gray-700;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .base-sidebar {
    @apply fixed left-0 top-0 z-50;
  }
}
</style>
