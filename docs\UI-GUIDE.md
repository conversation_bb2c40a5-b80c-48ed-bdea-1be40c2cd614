# 🎨 UI 設計系統指南

## 📋 目錄
- [設計原則](#設計原則)
- [色彩系統](#色彩系統)
- [字體系統](#字體系統)
- [間距系統](#間距系統)
- [元件規範](#元件規範)
- [響應式設計](#響應式設計)
- [重構策略](#重構策略)

## 🎯 設計原則

### 現有問題分析
1. **字體與層級**：字型大小不一致、主次層級不明，0.00 kW 太粗、單位過小
2. **色彩與對比**：橘色雖明顯，但過度使用顯得老舊（尤其底部告警區）
3. **空間與間距**：各元件之間間距過小，容易造成壓迫感
4. **導覽結構**：側邊選單無分群，過多選項顯得雜亂
5. **元件樣式**：按鈕、圖表、卡片沒有統一風格，圖表/指標也略顯過時
6. **響應式設計**：尚未檢視是否有支援手機／平板，字距與比例有問題的可能性高

### 設計目標
- 建立現代化、商業化的視覺風格
- 統一所有元件的設計語言
- 提升使用者體驗和操作效率
- 支援多裝置響應式設計
- 建立可維護的設計系統

## 🎨 色彩系統

### 主色調配置
```css
/* 主色系 - 橘色系列 */
--primary-50: #fff7ed;
--primary-100: #ffedd5;
--primary-200: #fed7aa;
--primary-300: #fdba74;
--primary-400: #fb923c;
--primary-500: #f97316;  /* 主色 */
--primary-600: #ea580c;
--primary-700: #c2410c;
--primary-800: #9a3412;
--primary-900: #7c2d12;

/* 次色系 - 灰藍色系列 */
--secondary-50: #f8fafc;
--secondary-100: #f1f5f9;
--secondary-200: #e2e8f0;
--secondary-300: #cbd5e1;
--secondary-400: #94a3b8;
--secondary-500: #64748b;
--secondary-600: #475569;
--secondary-700: #334155;
--secondary-800: #1e293b;  /* 次色 */
--secondary-900: #0f172a;
```

### Tailwind CSS 對應
| 用途 | 色碼 | Tailwind 類別 |
|------|------|---------------|
| 主色（橘） | #f97316 | `bg-orange-500` |
| 次色（灰藍） | #1e293b | `bg-slate-800` |
| 背景色 | #f9fafb | `bg-gray-50` |
| 成功色 | #10b981 | `bg-emerald-500` |
| 警告色 | #f59e0b | `bg-amber-500` |
| 錯誤色 | #ef4444 | `bg-red-500` |
| 資訊色 | #3b82f6 | `bg-blue-500` |

## 📝 字體系統

### 字體族群
```css
/* 主要字體 */
font-family: 'Noto Sans TC', 'Inter', 'Roboto', system-ui, sans-serif;

/* 等寬字體（用於代碼、數據） */
font-family: 'JetBrains Mono', 'Fira Code', monospace;
```

### 字體層級
| 層級 | 大小 | 行高 | 用途 | Tailwind 類別 |
|------|------|------|------|---------------|
| H1 | 2.25rem (36px) | 2.5rem | 主標題 | `text-4xl` |
| H2 | 1.875rem (30px) | 2.25rem | 次標題 | `text-3xl` |
| H3 | 1.5rem (24px) | 2rem | 區塊標題 | `text-2xl` |
| H4 | 1.25rem (20px) | 1.75rem | 小標題 | `text-xl` |
| Body | 1rem (16px) | 1.5rem | 內文 | `text-base` |
| Small | 0.875rem (14px) | 1.25rem | 輔助文字 | `text-sm` |
| Caption | 0.75rem (12px) | 1rem | 說明文字 | `text-xs` |

### 字重規範
| 用途 | 字重 | Tailwind 類別 |
|------|------|---------------|
| 標題 | 600 (Semi Bold) | `font-semibold` |
| 強調 | 500 (Medium) | `font-medium` |
| 一般 | 400 (Regular) | `font-normal` |
| 輔助 | 300 (Light) | `font-light` |

## 📏 間距系統

### 基礎間距單位
```css
/* 基礎單位：4px */
--spacing-1: 0.25rem;  /* 4px */
--spacing-2: 0.5rem;   /* 8px */
--spacing-3: 0.75rem;  /* 12px */
--spacing-4: 1rem;     /* 16px */
--spacing-5: 1.25rem;  /* 20px */
--spacing-6: 1.5rem;   /* 24px */
--spacing-8: 2rem;     /* 32px */
--spacing-10: 2.5rem;  /* 40px */
--spacing-12: 3rem;    /* 48px */
--spacing-16: 4rem;    /* 64px */
```

### 佈局間距規範
| 用途 | 間距 | Tailwind 類別 |
|------|------|---------------|
| 容器內邊距 | 16px-32px | `px-4 sm:px-6 lg:px-8` |
| 卡片內邊距 | 24px | `p-6` |
| 元件間距 | 16px | `space-y-4` |
| 區塊間距 | 32px | `space-y-8` |
| 頁面邊距 | 24px | `m-6` |

## 🧱 元件規範

### 按鈕 (BaseButton)
```vue
<!-- 主要按鈕 -->
<button class="px-4 py-2 bg-orange-500 text-white rounded-xl shadow-md hover:bg-orange-600 transition-colors">
  主要按鈕
</button>

<!-- 次要按鈕 -->
<button class="px-4 py-2 bg-slate-100 text-slate-700 rounded-xl shadow-md hover:bg-slate-200 transition-colors">
  次要按鈕
</button>
```

### 卡片 (BaseCard)
```vue
<div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
  <!-- 卡片內容 -->
</div>
```

### 表格 (BaseTable)
```vue
<div class="bg-white rounded-2xl shadow-lg overflow-hidden">
  <table class="table-auto w-full text-left">
    <!-- 表格內容 -->
  </table>
</div>
```

### 側邊欄 (BaseSidebar)
```vue
<aside class="w-64 bg-slate-800 text-white min-h-screen">
  <!-- 導覽內容 -->
</aside>
```

## 📱 響應式設計

### 斷點系統
| 裝置 | 寬度 | Tailwind 斷點 |
|------|------|---------------|
| 手機 | < 640px | `sm:` 以下 |
| 平板 | 640px - 1024px | `md:` |
| 桌面 | > 1024px | `lg:` 以上 |

### 響應式佈局
```css
/* 網格系統 */
.grid-responsive {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

/* 容器響應式 */
.container-responsive {
  @apply px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto;
}
```

## 🚀 重構策略

### 階段一：基礎設施建立
1. 安裝 Naive UI 和 Tailwind CSS
2. 配置主題色彩和字體
3. 建立基礎元件庫

### 階段二：核心元件開發
1. BaseCard - 資訊卡片元件
2. BaseButton - 標準化按鈕
3. BaseTable - 可排序/分頁的資料表格
4. BaseChart - ECharts 圖表封裝
5. BaseSidebar - 側邊導覽分群
6. BaseHeader - 頂部導覽列

### 階段三：頁面重構
1. 儀表板頁面
2. 資料表格頁面
3. 圖表分析頁面
4. 設定頁面

### 階段四：優化與測試
1. 響應式測試
2. 效能優化
3. 使用者體驗測試
4. 瀏覽器相容性測試

## 📦 技術棧整合

### 主要依賴
- **UI 框架**: Naive UI (Vue 3 友好)
- **樣式框架**: Tailwind CSS
- **圖表庫**: ECharts/VCharts
- **圖示庫**: Lucide Vue / Tabler Icons

### 配置檔案
- `tailwind.config.js` - Tailwind 配置
- `naive-ui.config.js` - Naive UI 主題配置
- `components.config.js` - 元件註冊配置

## 🎯 品牌與微互動

### 微互動設計
- 所有按鈕、hover、點擊都有 `transition-colors duration-200`
- 卡片 hover 效果：`hover:shadow-xl transition-shadow`
- 載入動畫：使用 Naive UI 的 Loading 元件

### 品牌元素
- 公司 LOGO 整合
- 主視覺色彩應用
- 一致的圓角設計 (`rounded-xl`)
- 統一的陰影效果 (`shadow-lg`)

---

*此設計系統將作為整個 UI 重構專案的基礎規範，所有開發人員都應遵循此指南進行開發。*
