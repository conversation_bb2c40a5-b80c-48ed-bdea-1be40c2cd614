# 📋 專案開發規則文檔

## 🎯 總則

本文檔定義了 PLC 能源管理系統 2.0 的開發規則、編碼標準、工作流程和品質要求。所有參與專案的開發人員都必須嚴格遵守這些規則。

## 🔧 環境與工具要求

### 必要環境版本
- **Node.js**: 18.16.1 （嚴格要求，不可使用其他版本）
- **npm**: 9.5.1 （嚴格要求，不可使用其他版本）
- **Git**: 2.30+ 
- **瀏覽器**: Chrome 90+, Firefox 88+, Safari 14+

### 開發工具標準
- **IDE**: VS Code（推薦）
- **擴展套件**:
  - Vue Language Features (Volar)
  - ESLint
  - Prettier
  - GitLens
  - Auto Rename Tag
  - Bracket Pair Colorizer

## 📝 編碼規範

### JavaScript/Vue 編碼標準

#### 1. 命名規範
```javascript
// ✅ 正確：組件名使用 PascalCase
const UserProfile = defineComponent({...})

// ✅ 正確：變數和函數使用 camelCase
const userName = 'john'
const getUserData = () => {...}

// ✅ 正確：常數使用 UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com'

// ✅ 正確：文件名使用 kebab-case
// user-profile.vue
// api-service.js
```

#### 2. Vue 組件規範
```vue
<!-- ✅ 正確的組件結構 -->
<template>
  <div class="component-wrapper">
    <!-- 模板內容 -->
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'

export default defineComponent({
  name: 'ComponentName',
  props: {
    // props 定義
  },
  emits: ['event-name'],
  setup(props, { emit }) {
    // 組合式 API 邏輯
    return {
      // 返回值
    }
  }
})
</script>

<style scoped>
/* 樣式定義 */
</style>
```

#### 3. 代碼格式化
- 使用 Prettier 進行自動格式化
- 縮排：2 個空格
- 字串：使用單引號
- 行尾：不使用分號（除非必要）
- 最大行長：100 字符

### CSS/SCSS 規範

#### 1. 類別命名
```css
/* ✅ 正確：使用 BEM 命名法 */
.card {}
.card__header {}
.card__body {}
.card--large {}

/* ✅ 正確：使用 Tailwind CSS 類別 */
.bg-orange-500 {}
.text-slate-800 {}
.rounded-xl {}
```

#### 2. 樣式組織
```scss
// ✅ 正確的樣式結構
.component {
  // 佈局屬性
  display: flex;
  position: relative;
  
  // 尺寸屬性
  width: 100%;
  height: auto;
  
  // 外觀屬性
  background-color: #ffffff;
  border-radius: 8px;
  
  // 文字屬性
  font-size: 16px;
  color: #333333;
  
  // 嵌套選擇器
  &__element {
    // 子元素樣式
  }
  
  &--modifier {
    // 修飾符樣式
  }
}
```

## 🌿 Git 工作流程

### 分支策略
```
main                    # 主分支（生產環境）
├── develop            # 開發分支
├── feature/xxx        # 功能分支
├── hotfix/xxx         # 緊急修復分支
└── release/xxx        # 發布分支
```

### 提交訊息規範
```bash
# 格式：<type>(<scope>): <description>

# 類型說明：
feat:     # 新功能
fix:      # 修復 bug
docs:     # 文檔更新
style:    # 代碼格式化（不影響功能）
refactor: # 重構代碼
test:     # 測試相關
chore:    # 建置工具、輔助工具變動

# 範例：
feat(dashboard): add real-time data chart
fix(auth): resolve login token expiration issue
docs(api): update API documentation
style(components): format code with prettier
refactor(utils): optimize data processing functions
```

### Pull Request 規範
1. **標題格式**: `[類型] 簡短描述`
2. **描述內容**:
   - 變更摘要
   - 相關 Issue 編號
   - 測試說明
   - 截圖（如適用）

3. **審查要求**:
   - 至少需要 1 人審查
   - 所有測試必須通過
   - 代碼覆蓋率不得降低

## 🧪 測試規範

### 測試類型
1. **單元測試**: 使用 Jest
2. **組件測試**: 使用 Vue Test Utils
3. **E2E 測試**: 使用 Cypress
4. **API 測試**: 使用 Postman/Newman

### 測試覆蓋率要求
- **整體覆蓋率**: ≥ 80%
- **函數覆蓋率**: ≥ 85%
- **分支覆蓋率**: ≥ 75%
- **行覆蓋率**: ≥ 80%

### 測試命名規範
```javascript
// ✅ 正確的測試命名
describe('UserService', () => {
  describe('getUserById', () => {
    it('should return user data when valid ID is provided', () => {
      // 測試邏輯
    })
    
    it('should throw error when invalid ID is provided', () => {
      // 測試邏輯
    })
  })
})
```

## 📦 依賴管理規範

### 新增依賴流程
1. 評估依賴必要性
2. 檢查授權條款
3. 評估安全性
4. 檢查維護狀態
5. 團隊討論決定
6. 更新文檔

### 依賴版本管理
- 使用 `package-lock.json` 鎖定版本
- 定期更新依賴（每月檢查）
- 重大版本更新需團隊討論
- 安全性更新優先處理

## 🎨 UI/UX 開發規範

### 設計系統遵循
- 嚴格遵循 [UI 設計指南](./UI-GUIDE.md)
- 使用統一的色彩系統
- 遵循字體和間距規範
- 保持元件一致性

### 響應式設計要求
```css
/* 斷點定義 */
@media (max-width: 640px)  { /* 手機 */ }
@media (min-width: 641px) and (max-width: 1024px) { /* 平板 */ }
@media (min-width: 1025px) { /* 桌面 */ }
```

### 無障礙設計
- 支援鍵盤導航
- 提供適當的 ARIA 標籤
- 確保色彩對比度 ≥ 4.5:1
- 支援螢幕閱讀器

## 🔒 安全規範

### 前端安全
- 避免 XSS 攻擊
- 驗證所有用戶輸入
- 使用 HTTPS
- 敏感資料不存於前端

### 代碼安全
- 定期執行安全掃描
- 使用 ESLint 安全規則
- 避免使用 `eval()` 和 `innerHTML`
- 驗證第三方依賴安全性

## 📊 效能規範

### 效能指標要求
- **首次內容繪製 (FCP)**: < 1.5s
- **最大內容繪製 (LCP)**: < 2.5s
- **首次輸入延遲 (FID)**: < 100ms
- **累積佈局偏移 (CLS)**: < 0.1

### 優化策略
- 代碼分割和懶加載
- 圖片優化和壓縮
- 使用 CDN
- 快取策略實施

## 📋 代碼審查清單

### 功能性檢查
- [ ] 功能是否正常運作
- [ ] 是否處理錯誤情況
- [ ] 是否有適當的驗證
- [ ] 是否符合需求規格

### 代碼品質檢查
- [ ] 代碼是否清晰易讀
- [ ] 是否遵循命名規範
- [ ] 是否有適當的註解
- [ ] 是否避免重複代碼

### 效能檢查
- [ ] 是否有效能問題
- [ ] 是否有記憶體洩漏
- [ ] 是否有不必要的重新渲染
- [ ] 是否有適當的快取

### 安全性檢查
- [ ] 是否有安全漏洞
- [ ] 是否驗證用戶輸入
- [ ] 是否有適當的權限控制
- [ ] 是否洩漏敏感資訊

## 🚫 禁止事項

### 絕對禁止
- 直接在 `main` 分支提交代碼
- 跳過代碼審查流程
- 提交包含密碼或 API 金鑰的代碼
- 使用非指定版本的 Node.js/npm
- 修改 `.gitignore` 中的核心規則

### 強烈不建議
- 使用 `console.log` 在生產代碼中
- 使用 `any` 類型（TypeScript）
- 內聯樣式（除非必要）
- 過度嵌套的組件結構
- 未經測試的代碼提交

## 📞 違規處理

### 輕微違規
- 口頭提醒
- 代碼審查要求修改
- 提供改進建議

### 嚴重違規
- 書面警告
- 強制培訓
- 暫停提交權限

### 重大違規
- 專案負責人介入
- 團隊會議討論
- 可能的紀律處分

---

**📝 注意事項**
- 本規則文檔會定期更新
- 所有變更都會通知團隊
- 如有疑問請聯絡專案負責人
