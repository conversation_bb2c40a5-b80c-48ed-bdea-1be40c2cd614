"use strict";(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[2118,3003],{45118:function(e,a,n){n.d(a,{A:function(){return f}});var t=n(20641),l=n(72644);const i={class:"ninjadash-datatable-filter"},o={key:0,class:"ninjadash-datatable-filter__right"},d={class:"ninjadasj-datatable"};function s(e,a,n,s,r,u){const c=(0,t.g2)("sdButton"),p=(0,t.g2)("a-space"),m=(0,t.g2)("unicon"),h=(0,t.g2)("a-input"),g=(0,t.g2)("a-button"),b=(0,t.g2)("a-col"),f=(0,t.g2)("a-row"),y=(0,t.g2)("a-table"),k=(0,t.g2)("TableWrapper"),v=(0,t.g2)("DataTableStyleWrap");return(0,t.uX)(),(0,t.Wv)(v,null,{default:(0,t.k6)((()=>[(0,t.Lk)("div",i,[(0,t.Lk)("div",null,[(0,t.bF)(p,null,{default:(0,t.k6)((()=>[e.addOption?((0,t.uX)(),(0,t.Wv)(c,{key:0,class:"act-btn",type:"primary",onClick:e.handleAdd},{default:(0,t.k6)((()=>[(0,t.eW)(" 新增 ")])),_:1},8,["onClick"])):(0,t.Q3)("",!0),e.backOption?((0,t.uX)(),(0,t.Wv)(c,{key:1,size:"default",outlined:!0,type:"primary",onClick:e.handleBack},{default:(0,t.k6)((()=>[(0,t.eW)(" 回上層 "+(0,l.v_)(e.backTitle),1)])),_:1},8,["onClick"])):(0,t.Q3)("",!0)])),_:1})]),e.filterOption?((0,t.uX)(),(0,t.CE)("div",o,[(0,t.bF)(h,{onChange:e.handleDataSearch,size:"default",placeholder:"搜尋"},{prefix:(0,t.k6)((()=>[(0,t.bF)(m,{name:"search"})])),_:1},8,["onChange"])])):(0,t.Q3)("",!0)]),(0,t.bF)(f,{align:"end"},{default:(0,t.k6)((()=>[e.importOption?((0,t.uX)(),(0,t.Wv)(b,{key:0,style:{"margin-bottom":"1rem"}},{default:(0,t.k6)((()=>[(0,t.bF)(g,{type:"primary",ghost:"",onClick:e.handleImport},{default:(0,t.k6)((()=>[(0,t.eW)("匯入")])),_:1},8,["onClick"])])),_:1})):(0,t.Q3)("",!0),e.exportOption?((0,t.uX)(),(0,t.Wv)(b,{key:1,style:{"margin-bottom":"1rem"}},{default:(0,t.k6)((()=>[(0,t.bF)(g,{type:"primary",ghost:"",onClick:e.handleExport},{default:(0,t.k6)((()=>[(0,t.eW)("匯出Excel")])),_:1},8,["onClick"])])),_:1})):(0,t.Q3)("",!0)])),_:1}),(0,t.Lk)("div",d,[(0,t.bF)(k,{class:"table-data-view table-responsive"},{default:(0,t.k6)((()=>[e.rowSelection?((0,t.uX)(),(0,t.Wv)(y,{key:0,class:"ant-table-striped","row-selection":e.rowSelections,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},childrenColumnName:e.childrenColumnName,"row-class-name":e.getRowClassName,"data-source":e.tableData,columns:e.columns,onChange:e.changePageSize},null,8,["row-selection","pagination","childrenColumnName","row-class-name","data-source","columns","onChange"])):((0,t.uX)(),(0,t.Wv)(y,{key:1,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},class:"ant-table-striped",childrenColumnName:e.childrenColumnName,"data-source":e.tableData,"row-class-name":e.getRowClassName,columns:e.columns,onChange:e.changePageSize},null,8,["pagination","childrenColumnName","data-source","row-class-name","columns","onChange"]))])),_:1})])])),_:1})}var r=n(79841),u=n(19732),c=n(95853);const p=c.Ay.div`
    .ninjadash-datatable-filter{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin: 20px 0 25px 0;
        @media only screen and (max-width: 575px){
            flex-wrap: wrap;
        }
        .ninjadash-datatable-filter__left{
            display: inline-flex;
            width: 100%;
            align-items: center;
            .ant-form{
                display: inline-flex;
                width: 100%;
                align-items: center;
            }
            span.label{
                margin-right: 8px;
                color: ${({theme:e})=>e[e.mainContent]["gray-text"]};
            }
            .ninjadash-datatable-filter__input{
                display: flex;
                align-items: center;
                padding-right: 20px;
                .ant-input{
                    height: 40px;
                }
            }
        }
        .ninjadash-datatable-filter__right{
            min-width: 280px;
            @media only screen and (max-width: 575px){
                margin-top: 15px;
            }
            .ant-input-affix-wrapper{
                padding: 7.22px 20px;
                border-radius: 6px;
                .ant-input-prefix{
                    svg{
                        width: 16px;
                        height: 16px;
                        fill: ${({theme:e})=>e[e.mainContent]["light-text"]};
                    }
                }
            }
        }
    }
`;var m=n(79570),h=(0,t.pM)({components:{DataTableStyleWrap:p,TableWrapper:m.AC},props:{filterOption:u.Ay.bool,filterOnchange:u.Ay.bool,rowSelection:u.Ay.bool,defaultSelected:u.Ay.array,tableData:u.Ay.array,columns:u.Ay.array,handleDataSearch:u.Ay.func,handleAdd:u.Ay.func,handleBack:u.Ay.func,handleImport:u.Ay.func,handleExport:u.Ay.func,rowClassFunc:{type:Function,default:()=>{}},backOption:{type:Boolean,default:!1},addOption:{type:Boolean,default:!1},exportOption:{type:Boolean,default:!1},importOption:{type:Boolean,default:!1},expandedRow:{type:Object,default:null},childrenColumnName:{type:String,default:"children"},backTitle:{type:String,default:""}},setup(e,{emit:a}){const n=(0,r.KR)([]);(0,t.wB)((()=>e.defaultSelected),(e=>{n.value=e}),{immediate:!0});const l=e=>{n.value=e,a("onSelectChange",n.value)},i=(0,t.EW)((()=>({selectedRowKeys:(0,r.R1)(n),onChange:l,hideDefaultSelections:!0}))),o=(0,r.KR)(10),d=(0,r.KR)(["10","20","50","100"]),s=e=>{o.value=e.pageSize},u=({record:a})=>e.expandedRow.innerDataProp?a[e.expandedRow.innerDataProp]:a.children,c=(a,n)=>e.rowClassFunc(a)??n%2===1?"table-striped row-style":"row-style";return{pageSize:o,pageSizeOptions:d,rowSelections:i,changePageSize:s,getInnerData:u,getRowClassName:c}}}),g=n(66262);const b=(0,g.A)(h,[["render",s],["__scopeId","data-v-1b881e36"]]);var f=b},52118:function(e,a,n){n.r(a),n.d(a,{default:function(){return y}});var t=n(20641),l=n(72644),i=n(9322);const o={key:0};function d(e,a,n,d,s,r){const u=(0,t.g2)("sdPageHeader"),c=(0,t.g2)("a-input"),p=(0,t.g2)("a-form-item"),m=(0,t.g2)("a-checkbox"),h=(0,t.g2)("a-table"),g=(0,t.g2)("a-button"),b=(0,t.g2)("a-col"),f=(0,t.g2)("a-spin"),y=(0,t.g2)("a-row"),k=(0,t.g2)("a-form"),v=(0,t.g2)("sdModal"),x=(0,t.g2)("DataTables"),C=(0,t.g2)("sdCards"),w=(0,t.g2)("Main");return(0,t.uX)(),(0,t.CE)("div",null,[(0,t.bF)(u,{title:"權限設定",class:"ninjadash-page-header-main",routes:[{breadcrumbName:"人員"},{breadcrumbName:"權限設定"}]}),(0,t.bF)(w,null,{default:(0,t.k6)((()=>[e.modal?((0,t.uX)(),(0,t.Wv)(v,{key:0,title:e.formState.title,visible:e.modal,onCancel:e.closeModal},{default:(0,t.k6)((()=>[(0,t.bF)(k,{model:e.formState,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,rules:e.rules,labelAlign:"left",onFinish:e.submitForm},{default:(0,t.k6)((()=>[(0,t.bF)(p,{label:"名稱",name:"name"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{value:e.formState.name,"onUpdate:value":a[0]||(a[0]=a=>e.formState.name=a),placeholder:"請輸入名稱"},null,8,["value"])])),_:1}),(0,t.bF)(h,{columns:e.permissionColumns,"data-source":e.permissionTable,pagination:!1,rowKey:e=>e.id,expandedRowKeys:e.expandedRowKeys,childrenColumnName:"sub",onExpand:e.expandRow},{bodyCell:(0,t.k6)((({text:a,record:n,column:i})=>["name"===i.key?((0,t.uX)(),(0,t.CE)("p",o,(0,l.v_)(a),1)):(0,t.Q3)("",!0),"view"===i.key&&e.hasOption(n.id,"r")?((0,t.uX)(),(0,t.Wv)(m,{key:1,checked:n.view,onChange:a=>e.changeCheckbox(a,{id:n.id,value:"r"})},null,8,["checked","onChange"])):(0,t.Q3)("",!0),"add"===i.key&&e.hasOption(n.id,"c")?((0,t.uX)(),(0,t.Wv)(m,{key:2,checked:n.add,onChange:a=>e.changeCheckbox(a,{id:n.id,value:"c"})},null,8,["checked","onChange"])):(0,t.Q3)("",!0),"edit"===i.key&&e.hasOption(n.id,"u")?((0,t.uX)(),(0,t.Wv)(m,{key:3,checked:n.edit,onChange:a=>e.changeCheckbox(a,{id:n.id,value:"u"})},null,8,["checked","onChange"])):(0,t.Q3)("",!0),"delete"===i.key&&e.hasOption(n.id,"d")?((0,t.uX)(),(0,t.Wv)(m,{key:4,checked:n.delete,onChange:a=>e.changeCheckbox(a,{id:n.id,value:"d"})},null,8,["checked","onChange"])):(0,t.Q3)("",!0)])),_:1},8,["columns","data-source","rowKey","expandedRowKeys","onExpand"]),(0,t.bF)(y,{gutter:[10,10],justify:"center",style:{"margin-top":"1rem"}},{default:(0,t.k6)((()=>[(0,t.bF)(b,null,{default:(0,t.k6)((()=>[(0,t.bF)(g,{type:"primary",ghost:"",style:{height:"40px"},onClick:(0,i.D$)(e.closeModal,["prevent"])},{default:(0,t.k6)((()=>[(0,t.eW)("取消")])),_:1},8,["onClick"])])),_:1}),(0,t.bF)(b,null,{default:(0,t.k6)((()=>[(0,t.bF)(g,{"html-type":"submit",type:"primary",style:{height:"40px"},disabled:e.loading},{default:(0,t.k6)((()=>[(0,t.eW)("儲存"),e.loading?((0,t.uX)(),(0,t.Wv)(f,{key:0,size:"small"})):(0,t.Q3)("",!0)])),_:1},8,["disabled"])])),_:1})])),_:1})])),_:1},8,["model","label-col","wrapper-col","rules","onFinish"])])),_:1},8,["title","visible","onCancel"])):(0,t.Q3)("",!0),(0,t.bF)(C,null,{default:(0,t.k6)((()=>[e.loading?((0,t.uX)(),(0,t.Wv)(f,{key:0})):(0,t.Q3)("",!0),e.loading?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.Wv)(x,{key:1,filterOption:!0,filterOnchange:!0,tableData:e.tableData,columns:e.columns,rowSelection:!1,addOption:e.permission.create,handleAdd:e.openAddModal,handleDataSearch:e.search},null,8,["tableData","columns","addOption","handleAdd","handleDataSearch"]))])),_:1})])),_:1})])}n(1532);var s=n(30995),r=(n(75126),n(56427)),u=(n(44114),n(18111),n(20116),n(7588),n(61701),n(79570)),c=n(79841),p=n(45118),m=n(40834),h=n(82958),g=(0,t.pM)({components:{Main:u.gZ,DataTables:p.A},setup(){const{permission:e}=(0,h.J)(),{state:a,dispatch:n}=(0,m.Pj)(),l=(0,t.EW)((()=>a.user.loading));(0,t.sV)((async()=>{n("user/getRoleList");const e=await Promise.all([n("user/getRoleOptions")]);x.value=e[0].permission,e[0].permission.forEach((e=>{if(C.value.push(e),e.sub){const a=e.sub.map((a=>({...e,...a})));C.value.push(...a)}}))}));const i=[{title:"名稱",dataIndex:"name",key:"name"},{title:"操作",dataIndex:"action",key:"action"}],o=(0,t.EW)((()=>a.user.roleTableData.map((a=>({name:a.name,action:(0,t.bF)(u.J9,null,{default:()=>[e.update&&(0,t.bF)("span",{onClick:()=>W(a)},[(0,t.bF)((0,t.g2)("unicon"),{name:"edit"},null)]),e.delete&&(0,t.bF)("span",{onClick:()=>z(a.id)},[(0,t.bF)((0,t.g2)("unicon"),{name:"trash"},null)])]})}))))),d=e=>{n("user/filterRoleTable",e.target.value)},p={lg:6,md:9,xs:24},g={lg:18,md:15,xs:24},b=(0,c.Kh)({title:"",id:null,name:"",permission:{}}),f={name:[{required:!0,trigger:"blur",message:"請輸入名稱"}]},y=[{title:"名稱",dataIndex:"name",key:"name"},{title:"檢視",dataIndex:"view",key:"view"},{title:"新增",dataIndex:"add",key:"add"},{title:"編輯",dataIndex:"edit",key:"edit"},{title:"刪除",dataIndex:"delete",key:"delete"}],k=(0,c.KR)([]),v=(e,a)=>{if(k.value.length>0){let e=k.value.indexOf(a.id);e>-1?k.value.splice(e,1):(k.value.splice(0,k.value.length),k.value.push(a.id))}else k.value.push(a.id)},x=(0,c.KR)([]),C=(0,c.KR)([]),w=(0,t.EW)((()=>{const e=a=>{const n=a.map((a=>{const n=b.permission[a.id],t=n&&n.includes("r"),l=n&&n.includes("c"),i=n&&n.includes("u"),o=n&&n.includes("d");return{id:a.id,name:a.name,value:a.value,view:t,add:l,edit:i,sub:a.sub?e(a.sub):null,delete:o}}));return n};return e(x.value)})),S=e=>e.sub.map((e=>{const a=b.permission[e.id],n=a&&a.includes("r"),t=a&&a.includes("c"),l=a&&a.includes("u"),i=a&&a.includes("d");return{id:e.id,name:e.name,view:n,add:t,edit:l,sub:e.sub,delete:i}})),_=(e,a)=>{const n=C.value.find((a=>a.id===e));return n.options.includes(a)},O=(e,{id:a,value:n})=>{if(e.target.checked)b.permission[a]?b.permission[a].push(n):b.permission[a]=[n];else{const e=b.permission[a].indexOf(n);b.permission[a].splice(e,1)}},F=(0,c.KR)(!1),A=()=>{const e={title:"新增權限",id:null,name:"",permission:{}};Object.assign(b,e),F.value=!0},W=({id:e,name:a,permission:n})=>{const t={title:"編輯權限",id:e,name:a,permission:n};Object.assign(b,t),F.value=!0},R=async()=>{try{let e;b.id?(e="編輯成功",await n("user/editRole",(0,c.ux)(b))):(e="新增成功",await n("user/addRole",(0,c.ux)(b))),F.value=!1,r.A.success({message:e})}catch(e){s.A.error({title:"發生錯誤",content:e})}},D=()=>{F.value=!1},z=e=>{s.A.confirm({title:"確認刪除?",okText:"確認",cancelText:"取消",confirmLoading:l.value,onOk:async()=>{try{await n("user/deleteRole",e),r.A.success({message:"刪除成功"})}catch(a){s.A.error({title:"發生錯誤",content:a.message})}}})};return{permission:e,loading:l,columns:i,tableData:o,search:d,expandedRowKeys:k,expandRow:v,permissionOptions:x,labelCol:p,wrapperCol:g,formState:b,rules:f,permissionColumns:y,permissionTable:w,innerTable:S,hasOption:_,changeCheckbox:O,modal:F,openAddModal:A,submitForm:R,closeModal:D}}}),b=n(66262);const f=(0,b.A)(g,[["render",d]]);var y=f},75126:function(e,a,n){n(16859)},82958:function(e,a,n){n.d(a,{J:function(){return o}});var t=n(79841),l=n(92317),i=n(75220);function o(e){const{name:a}=(0,i.lq)();let n="";n=e||a;const o=(0,l.Gq)("permission"),d=(0,t.Kh)({read:o[n]?.includes("r"),create:o[n]?.includes("c"),update:o[n]?.includes("u"),delete:o[n]?.includes("d")});return{permission:d}}}}]);