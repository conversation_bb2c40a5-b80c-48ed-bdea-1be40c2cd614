(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[1143,3003,5891],{5759:function(e,t,a){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var n,l=r(a(52814));(function(e){function t(e){return new l.default(e)}function a(e){return"number"===typeof e.delete?e.delete:"number"===typeof e.retain?e.retain:"string"===typeof e.insert?e.insert.length:1}e.iterator=t,e.length=a})(n||(n={})),t["default"]=n},8142:function(e,t,a){e=a.nmd(e);var r=200,n="__lodash_hash_undefined__",l=1,o=2,u=9007199254740991,i="[object Arguments]",s="[object Array]",p="[object AsyncFunction]",c="[object Boolean]",d="[object Date]",f="[object Error]",v="[object Function]",b="[object GeneratorFunction]",m="[object Map]",h="[object Number]",g="[object Null]",y="[object Object]",_="[object Promise]",F="[object Proxy]",k="[object RegExp]",C="[object Set]",w="[object String]",S="[object Symbol]",x="[object Undefined]",O="[object WeakMap]",j="[object ArrayBuffer]",A="[object DataView]",I="[object Float32Array]",T="[object Float64Array]",L="[object Int8Array]",M="[object Int16Array]",U="[object Int32Array]",W="[object Uint8Array]",E="[object Uint8ClampedArray]",N="[object Uint16Array]",V="[object Uint32Array]",X=/[\\^$.*+?()[\]{}|]/g,H=/^\[object .+?Constructor\]$/,D=/^(?:0|[1-9]\d*)$/,q={};q[I]=q[T]=q[L]=q[M]=q[U]=q[W]=q[E]=q[N]=q[V]=!0,q[i]=q[s]=q[j]=q[c]=q[A]=q[d]=q[f]=q[v]=q[m]=q[h]=q[y]=q[k]=q[C]=q[w]=q[O]=!1;var P="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,R="object"==typeof self&&self&&self.Object===Object&&self,z=P||R||Function("return this")(),K=t&&!t.nodeType&&t,Q=K&&e&&!e.nodeType&&e,$=Q&&Q.exports===K,B=$&&P.process,G=function(){try{return B&&B.binding&&B.binding("util")}catch(e){}}(),J=G&&G.isTypedArray;function Y(e,t){var a=-1,r=null==e?0:e.length,n=0,l=[];while(++a<r){var o=e[a];t(o,a,e)&&(l[n++]=o)}return l}function Z(e,t){var a=-1,r=t.length,n=e.length;while(++a<r)e[n+a]=t[a];return e}function ee(e,t){var a=-1,r=null==e?0:e.length;while(++a<r)if(t(e[a],a,e))return!0;return!1}function te(e,t){var a=-1,r=Array(e);while(++a<e)r[a]=t(a);return r}function ae(e){return function(t){return e(t)}}function re(e,t){return e.has(t)}function ne(e,t){return null==e?void 0:e[t]}function le(e){var t=-1,a=Array(e.size);return e.forEach((function(e,r){a[++t]=[r,e]})),a}function oe(e,t){return function(a){return e(t(a))}}function ue(e){var t=-1,a=Array(e.size);return e.forEach((function(e){a[++t]=e})),a}var ie=Array.prototype,se=Function.prototype,pe=Object.prototype,ce=z["__core-js_shared__"],de=se.toString,fe=pe.hasOwnProperty,ve=function(){var e=/[^.]+$/.exec(ce&&ce.keys&&ce.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),be=pe.toString,me=RegExp("^"+de.call(fe).replace(X,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),he=$?z.Buffer:void 0,ge=z.Symbol,ye=z.Uint8Array,_e=pe.propertyIsEnumerable,Fe=ie.splice,ke=ge?ge.toStringTag:void 0,Ce=Object.getOwnPropertySymbols,we=he?he.isBuffer:void 0,Se=oe(Object.keys,Object),xe=Ot(z,"DataView"),Oe=Ot(z,"Map"),je=Ot(z,"Promise"),Ae=Ot(z,"Set"),Ie=Ot(z,"WeakMap"),Te=Ot(Object,"create"),Le=Et(xe),Me=Et(Oe),Ue=Et(je),We=Et(Ae),Ee=Et(Ie),Ne=ge?ge.prototype:void 0,Ve=Ne?Ne.valueOf:void 0;function Xe(e){var t=-1,a=null==e?0:e.length;this.clear();while(++t<a){var r=e[t];this.set(r[0],r[1])}}function He(){this.__data__=Te?Te(null):{},this.size=0}function De(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}function qe(e){var t=this.__data__;if(Te){var a=t[e];return a===n?void 0:a}return fe.call(t,e)?t[e]:void 0}function Pe(e){var t=this.__data__;return Te?void 0!==t[e]:fe.call(t,e)}function Re(e,t){var a=this.__data__;return this.size+=this.has(e)?0:1,a[e]=Te&&void 0===t?n:t,this}function ze(e){var t=-1,a=null==e?0:e.length;this.clear();while(++t<a){var r=e[t];this.set(r[0],r[1])}}function Ke(){this.__data__=[],this.size=0}function Qe(e){var t=this.__data__,a=ft(t,e);if(a<0)return!1;var r=t.length-1;return a==r?t.pop():Fe.call(t,a,1),--this.size,!0}function $e(e){var t=this.__data__,a=ft(t,e);return a<0?void 0:t[a][1]}function Be(e){return ft(this.__data__,e)>-1}function Ge(e,t){var a=this.__data__,r=ft(a,e);return r<0?(++this.size,a.push([e,t])):a[r][1]=t,this}function Je(e){var t=-1,a=null==e?0:e.length;this.clear();while(++t<a){var r=e[t];this.set(r[0],r[1])}}function Ye(){this.size=0,this.__data__={hash:new Xe,map:new(Oe||ze),string:new Xe}}function Ze(e){var t=xt(this,e)["delete"](e);return this.size-=t?1:0,t}function et(e){return xt(this,e).get(e)}function tt(e){return xt(this,e).has(e)}function at(e,t){var a=xt(this,e),r=a.size;return a.set(e,t),this.size+=a.size==r?0:1,this}function rt(e){var t=-1,a=null==e?0:e.length;this.__data__=new Je;while(++t<a)this.add(e[t])}function nt(e){return this.__data__.set(e,n),this}function lt(e){return this.__data__.has(e)}function ot(e){var t=this.__data__=new ze(e);this.size=t.size}function ut(){this.__data__=new ze,this.size=0}function it(e){var t=this.__data__,a=t["delete"](e);return this.size=t.size,a}function st(e){return this.__data__.get(e)}function pt(e){return this.__data__.has(e)}function ct(e,t){var a=this.__data__;if(a instanceof ze){var n=a.__data__;if(!Oe||n.length<r-1)return n.push([e,t]),this.size=++a.size,this;a=this.__data__=new Je(n)}return a.set(e,t),this.size=a.size,this}function dt(e,t){var a=Xt(e),r=!a&&Vt(e),n=!a&&!r&&Dt(e),l=!a&&!r&&!n&&Qt(e),o=a||r||n||l,u=o?te(e.length,String):[],i=u.length;for(var s in e)!t&&!fe.call(e,s)||o&&("length"==s||n&&("offset"==s||"parent"==s)||l&&("buffer"==s||"byteLength"==s||"byteOffset"==s)||Tt(s,i))||u.push(s);return u}function ft(e,t){var a=e.length;while(a--)if(Nt(e[a][0],t))return a;return-1}function vt(e,t,a){var r=t(e);return Xt(e)?r:Z(r,a(e))}function bt(e){return null==e?void 0===e?x:g:ke&&ke in Object(e)?jt(e):Wt(e)}function mt(e){return Kt(e)&&bt(e)==i}function ht(e,t,a,r,n){return e===t||(null==e||null==t||!Kt(e)&&!Kt(t)?e!==e&&t!==t:gt(e,t,a,r,ht,n))}function gt(e,t,a,r,n,o){var u=Xt(e),p=Xt(t),c=u?s:It(e),d=p?s:It(t);c=c==i?y:c,d=d==i?y:d;var f=c==y,v=d==y,b=c==d;if(b&&Dt(e)){if(!Dt(t))return!1;u=!0,f=!1}if(b&&!f)return o||(o=new ot),u||Qt(e)?kt(e,t,a,r,n,o):Ct(e,t,c,a,r,n,o);if(!(a&l)){var m=f&&fe.call(e,"__wrapped__"),h=v&&fe.call(t,"__wrapped__");if(m||h){var g=m?e.value():e,_=h?t.value():t;return o||(o=new ot),n(g,_,a,r,o)}}return!!b&&(o||(o=new ot),wt(e,t,a,r,n,o))}function yt(e){if(!zt(e)||Mt(e))return!1;var t=Pt(e)?me:H;return t.test(Et(e))}function _t(e){return Kt(e)&&Rt(e.length)&&!!q[bt(e)]}function Ft(e){if(!Ut(e))return Se(e);var t=[];for(var a in Object(e))fe.call(e,a)&&"constructor"!=a&&t.push(a);return t}function kt(e,t,a,r,n,u){var i=a&l,s=e.length,p=t.length;if(s!=p&&!(i&&p>s))return!1;var c=u.get(e);if(c&&u.get(t))return c==t;var d=-1,f=!0,v=a&o?new rt:void 0;u.set(e,t),u.set(t,e);while(++d<s){var b=e[d],m=t[d];if(r)var h=i?r(m,b,d,t,e,u):r(b,m,d,e,t,u);if(void 0!==h){if(h)continue;f=!1;break}if(v){if(!ee(t,(function(e,t){if(!re(v,t)&&(b===e||n(b,e,a,r,u)))return v.push(t)}))){f=!1;break}}else if(b!==m&&!n(b,m,a,r,u)){f=!1;break}}return u["delete"](e),u["delete"](t),f}function Ct(e,t,a,r,n,u,i){switch(a){case A:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case j:return!(e.byteLength!=t.byteLength||!u(new ye(e),new ye(t)));case c:case d:case h:return Nt(+e,+t);case f:return e.name==t.name&&e.message==t.message;case k:case w:return e==t+"";case m:var s=le;case C:var p=r&l;if(s||(s=ue),e.size!=t.size&&!p)return!1;var v=i.get(e);if(v)return v==t;r|=o,i.set(e,t);var b=kt(s(e),s(t),r,n,u,i);return i["delete"](e),b;case S:if(Ve)return Ve.call(e)==Ve.call(t)}return!1}function wt(e,t,a,r,n,o){var u=a&l,i=St(e),s=i.length,p=St(t),c=p.length;if(s!=c&&!u)return!1;var d=s;while(d--){var f=i[d];if(!(u?f in t:fe.call(t,f)))return!1}var v=o.get(e);if(v&&o.get(t))return v==t;var b=!0;o.set(e,t),o.set(t,e);var m=u;while(++d<s){f=i[d];var h=e[f],g=t[f];if(r)var y=u?r(g,h,f,t,e,o):r(h,g,f,e,t,o);if(!(void 0===y?h===g||n(h,g,a,r,o):y)){b=!1;break}m||(m="constructor"==f)}if(b&&!m){var _=e.constructor,F=t.constructor;_==F||!("constructor"in e)||!("constructor"in t)||"function"==typeof _&&_ instanceof _&&"function"==typeof F&&F instanceof F||(b=!1)}return o["delete"](e),o["delete"](t),b}function St(e){return vt(e,$t,At)}function xt(e,t){var a=e.__data__;return Lt(t)?a["string"==typeof t?"string":"hash"]:a.map}function Ot(e,t){var a=ne(e,t);return yt(a)?a:void 0}function jt(e){var t=fe.call(e,ke),a=e[ke];try{e[ke]=void 0;var r=!0}catch(l){}var n=be.call(e);return r&&(t?e[ke]=a:delete e[ke]),n}Xe.prototype.clear=He,Xe.prototype["delete"]=De,Xe.prototype.get=qe,Xe.prototype.has=Pe,Xe.prototype.set=Re,ze.prototype.clear=Ke,ze.prototype["delete"]=Qe,ze.prototype.get=$e,ze.prototype.has=Be,ze.prototype.set=Ge,Je.prototype.clear=Ye,Je.prototype["delete"]=Ze,Je.prototype.get=et,Je.prototype.has=tt,Je.prototype.set=at,rt.prototype.add=rt.prototype.push=nt,rt.prototype.has=lt,ot.prototype.clear=ut,ot.prototype["delete"]=it,ot.prototype.get=st,ot.prototype.has=pt,ot.prototype.set=ct;var At=Ce?function(e){return null==e?[]:(e=Object(e),Y(Ce(e),(function(t){return _e.call(e,t)})))}:Bt,It=bt;function Tt(e,t){return t=null==t?u:t,!!t&&("number"==typeof e||D.test(e))&&e>-1&&e%1==0&&e<t}function Lt(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}function Mt(e){return!!ve&&ve in e}function Ut(e){var t=e&&e.constructor,a="function"==typeof t&&t.prototype||pe;return e===a}function Wt(e){return be.call(e)}function Et(e){if(null!=e){try{return de.call(e)}catch(t){}try{return e+""}catch(t){}}return""}function Nt(e,t){return e===t||e!==e&&t!==t}(xe&&It(new xe(new ArrayBuffer(1)))!=A||Oe&&It(new Oe)!=m||je&&It(je.resolve())!=_||Ae&&It(new Ae)!=C||Ie&&It(new Ie)!=O)&&(It=function(e){var t=bt(e),a=t==y?e.constructor:void 0,r=a?Et(a):"";if(r)switch(r){case Le:return A;case Me:return m;case Ue:return _;case We:return C;case Ee:return O}return t});var Vt=mt(function(){return arguments}())?mt:function(e){return Kt(e)&&fe.call(e,"callee")&&!_e.call(e,"callee")},Xt=Array.isArray;function Ht(e){return null!=e&&Rt(e.length)&&!Pt(e)}var Dt=we||Gt;function qt(e,t){return ht(e,t)}function Pt(e){if(!zt(e))return!1;var t=bt(e);return t==v||t==b||t==p||t==F}function Rt(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=u}function zt(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Kt(e){return null!=e&&"object"==typeof e}var Qt=J?ae(J):_t;function $t(e){return Ht(e)?dt(e):Ft(e)}function Bt(){return[]}function Gt(){return!1}e.exports=qt},12660:function(e,t,a){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},n=r(a(70982)),l=r(a(67193)),o=r(a(8142)),u=r(a(99106)),i=r(a(5759)),s=String.fromCharCode(0),p=function(){function e(e){Array.isArray(e)?this.ops=e:null!=e&&Array.isArray(e.ops)?this.ops=e.ops:this.ops=[]}return e.prototype.insert=function(e,t){var a={};return"string"===typeof e&&0===e.length?this:(a.insert=e,null!=t&&"object"===typeof t&&Object.keys(t).length>0&&(a.attributes=t),this.push(a))},e.prototype.delete=function(e){return e<=0?this:this.push({delete:e})},e.prototype.retain=function(e,t){if(e<=0)return this;var a={retain:e};return null!=t&&"object"===typeof t&&Object.keys(t).length>0&&(a.attributes=t),this.push(a)},e.prototype.push=function(e){var t=this.ops.length,a=this.ops[t-1];if(e=l.default(e),"object"===typeof a){if("number"===typeof e.delete&&"number"===typeof a.delete)return this.ops[t-1]={delete:a.delete+e.delete},this;if("number"===typeof a.delete&&null!=e.insert&&(t-=1,a=this.ops[t-1],"object"!==typeof a))return this.ops.unshift(e),this;if(o.default(e.attributes,a.attributes)){if("string"===typeof e.insert&&"string"===typeof a.insert)return this.ops[t-1]={insert:a.insert+e.insert},"object"===typeof e.attributes&&(this.ops[t-1].attributes=e.attributes),this;if("number"===typeof e.retain&&"number"===typeof a.retain)return this.ops[t-1]={retain:a.retain+e.retain},"object"===typeof e.attributes&&(this.ops[t-1].attributes=e.attributes),this}}return t===this.ops.length?this.ops.push(e):this.ops.splice(t,0,e),this},e.prototype.chop=function(){var e=this.ops[this.ops.length-1];return e&&e.retain&&!e.attributes&&this.ops.pop(),this},e.prototype.filter=function(e){return this.ops.filter(e)},e.prototype.forEach=function(e){this.ops.forEach(e)},e.prototype.map=function(e){return this.ops.map(e)},e.prototype.partition=function(e){var t=[],a=[];return this.forEach((function(r){var n=e(r)?t:a;n.push(r)})),[t,a]},e.prototype.reduce=function(e,t){return this.ops.reduce(e,t)},e.prototype.changeLength=function(){return this.reduce((function(e,t){return t.insert?e+i.default.length(t):t.delete?e-t.delete:e}),0)},e.prototype.length=function(){return this.reduce((function(e,t){return e+i.default.length(t)}),0)},e.prototype.slice=function(t,a){void 0===t&&(t=0),void 0===a&&(a=1/0);var r=[],n=i.default.iterator(this.ops),l=0;while(l<a&&n.hasNext()){var o=void 0;l<t?o=n.next(t-l):(o=n.next(a-l),r.push(o)),l+=i.default.length(o)}return new e(r)},e.prototype.compose=function(t){var a=i.default.iterator(this.ops),r=i.default.iterator(t.ops),n=[],l=r.peek();if(null!=l&&"number"===typeof l.retain&&null==l.attributes){var s=l.retain;while("insert"===a.peekType()&&a.peekLength()<=s)s-=a.peekLength(),n.push(a.next());l.retain-s>0&&r.next(l.retain-s)}var p=new e(n);while(a.hasNext()||r.hasNext())if("insert"===r.peekType())p.push(r.next());else if("delete"===a.peekType())p.push(a.next());else{var c=Math.min(a.peekLength(),r.peekLength()),d=a.next(c),f=r.next(c);if("number"===typeof f.retain){var v={};"number"===typeof d.retain?v.retain=c:v.insert=d.insert;var b=u.default.compose(d.attributes,f.attributes,"number"===typeof d.retain);if(b&&(v.attributes=b),p.push(v),!r.hasNext()&&o.default(p.ops[p.ops.length-1],v)){var m=new e(a.rest());return p.concat(m).chop()}}else"number"===typeof f.delete&&"number"===typeof d.retain&&p.push(f)}return p.chop()},e.prototype.concat=function(t){var a=new e(this.ops.slice());return t.ops.length>0&&(a.push(t.ops[0]),a.ops=a.ops.concat(t.ops.slice(1))),a},e.prototype.diff=function(t,a){if(this.ops===t.ops)return new e;var r=[this,t].map((function(e){return e.map((function(a){if(null!=a.insert)return"string"===typeof a.insert?a.insert:s;var r=e===t?"on":"with";throw new Error("diff() called "+r+" non-document")})).join("")})),l=new e,p=n.default(r[0],r[1],a),c=i.default.iterator(this.ops),d=i.default.iterator(t.ops);return p.forEach((function(e){var t=e[1].length;while(t>0){var a=0;switch(e[0]){case n.default.INSERT:a=Math.min(d.peekLength(),t),l.push(d.next(a));break;case n.default.DELETE:a=Math.min(t,c.peekLength()),c.next(a),l.delete(a);break;case n.default.EQUAL:a=Math.min(c.peekLength(),d.peekLength(),t);var r=c.next(a),i=d.next(a);o.default(r.insert,i.insert)?l.retain(a,u.default.diff(r.attributes,i.attributes)):l.push(i).delete(a);break}t-=a}})),l.chop()},e.prototype.eachLine=function(t,a){void 0===a&&(a="\n");var r=i.default.iterator(this.ops),n=new e,l=0;while(r.hasNext()){if("insert"!==r.peekType())return;var o=r.peek(),u=i.default.length(o)-r.peekLength(),s="string"===typeof o.insert?o.insert.indexOf(a,u)-u:-1;if(s<0)n.push(r.next());else if(s>0)n.push(r.next(s));else{if(!1===t(n,r.next(1).attributes||{},l))return;l+=1,n=new e}}n.length()>0&&t(n,{},l)},e.prototype.invert=function(t){var a=new e;return this.reduce((function(e,r){if(r.insert)a.delete(i.default.length(r));else{if(r.retain&&null==r.attributes)return a.retain(r.retain),e+r.retain;if(r.delete||r.retain&&r.attributes){var n=r.delete||r.retain,l=t.slice(e,e+n);return l.forEach((function(e){r.delete?a.push(e):r.retain&&r.attributes&&a.retain(i.default.length(e),u.default.invert(r.attributes,e.attributes))})),e+n}}return e}),0),a.chop()},e.prototype.transform=function(t,a){if(void 0===a&&(a=!1),a=!!a,"number"===typeof t)return this.transformPosition(t,a);var r=t,n=i.default.iterator(this.ops),l=i.default.iterator(r.ops),o=new e;while(n.hasNext()||l.hasNext())if("insert"!==n.peekType()||!a&&"insert"===l.peekType())if("insert"===l.peekType())o.push(l.next());else{var s=Math.min(n.peekLength(),l.peekLength()),p=n.next(s),c=l.next(s);if(p.delete)continue;c.delete?o.push(c):o.retain(s,u.default.transform(p.attributes,c.attributes,a))}else o.retain(i.default.length(n.next()));return o.chop()},e.prototype.transformPosition=function(e,t){void 0===t&&(t=!1),t=!!t;var a=i.default.iterator(this.ops),r=0;while(a.hasNext()&&r<=e){var n=a.peekLength(),l=a.peekType();a.next(),"delete"!==l?("insert"===l&&(r<e||!t)&&(e+=n),r+=n):e-=Math.min(n,e-r)}return e},e.Op=i.default,e.AttributeMap=u.default,e}();e.exports=p},20592:function(e,t,a){"use strict";a.d(t,{A:function(){return F}});var r=a(20641),n=a(72644),l=a(9322);const o={key:1},u={key:2},i={key:3};function s(e,t,a,s,p,c){const d=(0,r.g2)("a-radio-group"),f=(0,r.g2)("a-form-item"),v=(0,r.g2)("a-col"),b=(0,r.g2)("a-row"),m=(0,r.g2)("a-select-option"),h=(0,r.g2)("a-select"),g=(0,r.g2)("a-input"),y=(0,r.g2)("TagFilter"),_=(0,r.g2)("sdButton"),F=(0,r.g2)("unicon"),k=(0,r.g2)("DeleteSpan"),C=(0,r.g2)("a-textarea"),w=(0,r.g2)("a-form");return(0,r.uX)(),(0,r.Wv)(w,{ref:"form",name:"dynamic_form_nest_item",model:e.expFormstate,labelAlign:"left"},{default:(0,r.k6)((()=>[(0,r.bF)(b,{gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(v,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(f,{labelCol:{sm:4},wrapperCol:{sm:20},label:"啟用運算式:",name:"status"},{default:(0,r.k6)((()=>[(0,r.bF)(d,{value:e.expFormstate.status,"onUpdate:value":t[0]||(t[0]=t=>e.expFormstate.status=t),options:e.statusOptions},null,8,["value","options"])])),_:1})])),_:1})])),_:1}),e.expFormstate.status?((0,r.uX)(),(0,r.Wv)(b,{key:0,gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(v,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(f,{labelCol:{sm:4},wrapperCol:{sm:5},label:"轉換方式:",name:"type"},{default:(0,r.k6)((()=>[(0,r.bF)(h,{value:e.expFormstate.type,"onUpdate:value":t[1]||(t[1]=t=>e.expFormstate.type=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.expressionTypeOptions,(e=>((0,r.uX)(),(0,r.Wv)(m,{value:e.Id,key:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1})):(0,r.Q3)("",!0),e.expFormstate.status&&1===e.expFormstate.type?((0,r.uX)(),(0,r.CE)("div",o,[(0,r.bF)(f,{labelCol:{sm:4},wrapperCol:{sm:5},label:"測點倍數",name:"valueMultiple"},{default:(0,r.k6)((()=>[(0,r.bF)(g,{value:e.expFormstate.valueMultiple,"onUpdate:value":t[2]||(t[2]=t=>e.expFormstate.valueMultiple=t),style:{height:"40px"}},null,8,["value"])])),_:1})])):(0,r.Q3)("",!0),!e.expFormstate.status||"2"!==e.expFormstate.type&&"3"!==e.expFormstate.type?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("div",u,[(0,r.bF)(b,{gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(v,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(f,{labelCol:{sm:8},wrapperCol:{sm:12},label:"轉換最大值",name:"transferMax"},{default:(0,r.k6)((()=>[(0,r.bF)(g,{value:e.expFormstate.transferMax,"onUpdate:value":t[3]||(t[3]=t=>e.expFormstate.transferMax=t),style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(v,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(f,{labelCol:{sm:8},wrapperCol:{sm:12},label:"轉換最小值",name:"transferMin"},{default:(0,r.k6)((()=>[(0,r.bF)(g,{value:e.expFormstate.transferMin,"onUpdate:value":t[4]||(t[4]=t=>e.expFormstate.transferMin=t),style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(v,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(f,{labelCol:{sm:8},wrapperCol:{sm:12},label:"最大值",name:"max"},{default:(0,r.k6)((()=>[(0,r.bF)(g,{value:e.expFormstate.max,"onUpdate:value":t[5]||(t[5]=t=>e.expFormstate.max=t),style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(v,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(f,{labelCol:{sm:8},wrapperCol:{sm:12},label:"最小值",name:"min"},{default:(0,r.k6)((()=>[(0,r.bF)(g,{value:e.expFormstate.min,"onUpdate:value":t[6]||(t[6]=t=>e.expFormstate.min=t),style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1})])),_:1})])),e.expFormstate.status&&"4"===e.expFormstate.type?((0,r.uX)(),(0,r.CE)("div",i,[(0,r.bF)(b,null,{default:(0,r.k6)((()=>[(0,r.bF)(v,{span:13},{default:(0,r.k6)((()=>[(0,r.bF)(f,{labelCol:{sm:7},wrapperCol:{sm:18},label:"選擇測點",name:"min"},{default:(0,r.k6)((()=>[(0,r.bF)(y,{selectedTags:e.currentTag,onSetTags:e.setTags},null,8,["selectedTags","onSetTags"])])),_:1})])),_:1}),(0,r.bF)(v,{span:5},{default:(0,r.k6)((()=>[(0,r.bF)(_,{type:"primary",disabled:!e.currentTag,style:{height:"40px"},onClick:(0,l.D$)(e.addTag,["prevent"])},{default:(0,r.k6)((()=>[(0,r.eW)(" 加入測點 ")])),_:1},8,["disabled","onClick"])])),_:1})])),_:1}),((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.expFormstate.tags,((t,a)=>((0,r.uX)(),(0,r.Wv)(b,{key:t.id,gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(v,{span:16},{default:(0,r.k6)((()=>[(0,r.bF)(f,{labelCol:{sm:6},wrapperCol:{sm:18},name:["tags",a,"tag"],label:"測點"},{default:(0,r.k6)((()=>[(0,r.bF)(g,{value:e.useTagInfo(t,"Name"),disabled:"",style:{height:"40px"}},null,8,["value"])])),_:2},1032,["name"])])),_:2},1024),(0,r.bF)(v,{span:6},{default:(0,r.k6)((()=>[(0,r.bF)(k,{onClick:(0,l.D$)((a=>e.removeTag(t)),["prevent"])},{default:(0,r.k6)((()=>[(0,r.bF)(F,{name:"minus-circle"})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)))),128)),(0,r.bF)(f,null,{default:(0,r.k6)((()=>[(0,r.bF)(C,{value:e.expressionContent,placeholder:"運算式(EX: @測點代稱1@ + @測點代稱2@)",rules:{required:!0,message:"請輸入運算式"},onInput:e.editContent},null,8,["value","onInput"])])),_:1})])):(0,r.Q3)("",!0)])),_:1},8,["model"])}a(1532);var p=a(30995),c=(a(18111),a(20116),a(61701),a(79841)),d=a(95853);const f=d.Ay.span`
    width:25px;
    height:25px;
    cursor:pointer;
    margin-right:0.5rem;
    top:7.5px;
    position:relative;
    .unicon{
        svg{
            storke:rgb(255, 77, 79);
            fill:rgb(255, 77, 79);
            width:25px;
            height:25px
        }
    }
`,v=d.Ay.div`
    display:flex;
    align-item:center;
    justify-content:center;
    .unicon{
        margin-right:0.2rem;
        svg{
            stroke:rgb(90, 95, 125);
            fill:rgb(90, 95, 125);
            width:22px;
            height:22px;
        }
    }
`;var b=a(40834),m=a(82046),h=a(82077),g=(0,r.pM)({components:{DeleteSpan:f,AddBtn:v,TagFilter:m.A},props:{expFormstate:{type:Object,default:null},expressionTypeOptions:{type:Array,default:()=>[]}},setup(e,{emit:t}){const{state:a}=(0,b.Pj)(),n=(0,r.WQ)("expForm"),l=(0,r.EW)((()=>a.tags.loading)),o=[{value:!0,label:"啟用"},{value:!1,label:"停用"}],u=(0,r.EW)((()=>e.expFormstate.content?e.expFormstate.content.replace(/@([^@]*)@/g,((e,t)=>(0,h.M)(t,"Name")?`@${(0,h.M)(t,"Name")}@`:e)):"")),i=(0,c.KR)([]),s=e=>{t("removeTag",e)},d=e=>{i.value=e.map((e=>e.id))},f=()=>{e.expFormstate.tags&&e.expFormstate.tags.find((e=>i.value.find((t=>t===e))))?p.A.error({content:"測點已存在"}):t("addTag",i.value)},v=e=>{t("editContent",e.target.value)};return{form:n,loading:l,statusOptions:o,expressionContent:u,currentTag:i,removeTag:s,setTags:d,addTag:f,editContent:v,useTagInfo:h.M}}}),y=a(66262);const _=(0,y.A)(g,[["render",s]]);var F=_},29284:function(e,t,a){"use strict";a.d(t,{A:function(){return O}});var r=a(20641),n=a(72644);function l(e,t,a,l,o,u){const i=(0,r.g2)("a-radio-group"),s=(0,r.g2)("a-form-item"),p=(0,r.g2)("a-col"),c=(0,r.g2)("a-tree-select"),d=(0,r.g2)("DeviceFilter"),f=(0,r.g2)("CCTVFilter"),v=(0,r.g2)("a-input"),b=(0,r.g2)("a-select-option"),m=(0,r.g2)("a-select"),h=(0,r.g2)("a-radio"),g=(0,r.g2)("Notice"),y=(0,r.g2)("a-row"),_=(0,r.g2)("a-form");return(0,r.uX)(),(0,r.Wv)(_,{ref:"form",model:e.formState,rules:e.rules,labelAlign:"left"},{default:(0,r.k6)((()=>[(0,r.bF)(y,{gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(p,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:9},wrapperCol:{sm:15},label:"測點狀態",name:"status"},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.status,"onUpdate:value":t[0]||(t[0]=t=>e.formState.status=t),options:e.statusOptions},null,8,["value","options"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},autoLink:!1,label:"地區",name:"region"},{default:(0,r.k6)((()=>[(0,r.bF)(c,{value:e.formState.region,"onUpdate:value":t[1]||(t[1]=t=>e.formState.region=t),style:{width:"100%"},"tree-data":e.locations,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"裝置",name:"device"},{default:(0,r.k6)((()=>[(0,r.bF)(d,{value:e.formState.device,onSetDevice:e.setDevice},null,8,["value","onSetDevice"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"CCTV",name:"cctv"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{values:e.formState.cctv,onSetCCTV:e.setCCTV},null,8,["values","onSetCCTV"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"測點名稱",name:"name"},{default:(0,r.k6)((()=>[(0,r.bF)(v,{value:e.formState.name,"onUpdate:value":t[2]||(t[2]=t=>e.formState.name=t),placeholder:"名稱",style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"說明",name:"description"},{default:(0,r.k6)((()=>[(0,r.bF)(v,{value:e.formState.description,"onUpdate:value":t[3]||(t[3]=t=>e.formState.description=t),placeholder:"說明",style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"PLC位址",name:"valueAddress"},{default:(0,r.k6)((()=>[(0,r.bF)(v,{value:e.formState.valueAddress,"onUpdate:value":t[4]||(t[4]=t=>e.formState.valueAddress=t),placeholder:"PLC位址(base1)",style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},autoLink:!1,label:"測點分類",name:"tagClass"},{default:(0,r.k6)((()=>[(0,r.bF)(c,{value:e.formState.tagClass,"onUpdate:value":t[5]||(t[5]=t=>e.formState.tagClass=t),style:{width:"100%"},"tree-data":e.classOptions,"tree-checkable":"","allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data"])])),_:1})])),_:1}),(0,r.bF)(p,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:8},wrapperCol:{sm:16},label:"測量單位",name:"unit"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.formState.unit,"onUpdate:value":t[6]||(t[6]=t=>e.formState.unit=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.unitOptions,(e=>((0,r.uX)(),(0,r.Wv)(b,{value:e.Id,key:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),(0,r.bF)(p,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:8},wrapperCol:{sm:16},label:"資料型別",name:"dataType"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.formState.dataType,"onUpdate:value":t[7]||(t[7]=t=>e.formState.dataType=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.dataTypeOptions,(e=>((0,r.uX)(),(0,r.Wv)(b,{value:e.Id,key:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"測點種類",name:"type"},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.type,"onUpdate:value":t[8]||(t[8]=t=>e.formState.type=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.typeOptions,(e=>((0,r.uX)(),(0,r.Wv)(h,{key:e.Id,value:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"測點用途",name:"usage"},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.usage,"onUpdate:value":t[9]||(t[9]=t=>e.formState.usage=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.usageOptions,((e,t)=>((0,r.uX)(),(0,r.Wv)(h,{key:t,value:t},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"接點種類",name:"closingContact"},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.closingContact,"onUpdate:value":t[10]||(t[10]=t=>e.formState.closingContact=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.closingContactOptions,((e,t)=>((0,r.uX)(),(0,r.Wv)(h,{key:t,value:t},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"存取權限",name:"saveType"},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.saveType,"onUpdate:value":t[11]||(t[11]=t=>e.formState.saveType=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.saveTypeOptions,(e=>((0,r.uX)(),(0,r.Wv)(h,{key:e.Id,value:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(s,{labelCol:{sm:4},wrapperCol:{sm:20},label:"儲存歷史",name:"log"},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.log,"onUpdate:value":t[12]||(t[12]=t=>e.formState.log=t),options:e.logOptions},null,8,["value","options"])])),_:1})])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[e.formState.log?((0,r.uX)(),(0,r.Wv)(s,{key:0,labelCol:{sm:8},wrapperCol:{sm:16},label:"儲存間隔模式",name:"logIntervalType"},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.logIntervalType,"onUpdate:value":t[13]||(t[13]=t=>e.formState.logIntervalType=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.logTypeOptions,(e=>((0,r.uX)(),(0,r.Wv)(h,{key:e.Id,value:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"]),(0,r.bF)(g,{content:e.noticeContent},null,8,["content"])])),_:1})):(0,r.Q3)("",!0)])),_:1}),(0,r.bF)(p,{span:24},{default:(0,r.k6)((()=>[e.formState.log&&3!==e.formState.logIntervalType?((0,r.uX)(),(0,r.Wv)(s,{key:0,labelCol:{sm:8},wrapperCol:{sm:16},label:"儲存間隔時間(分鐘)",name:"logInterval"},{default:(0,r.k6)((()=>[(0,r.bF)(v,{value:e.formState.logInterval,"onUpdate:value":t[14]||(t[14]=t=>e.formState.logInterval=t),style:{height:"40px"}},null,8,["value"])])),_:1})):(0,r.Q3)("",!0)])),_:1})])),_:1})])),_:1},8,["model","rules"])}var o=a(40834),u=a(79841),i=a(94734);function s(e,t,a,l,o,u){const i=(0,r.g2)("a-tree-select"),s=(0,r.g2)("a-form-item"),p=(0,r.g2)("a-form"),c=(0,r.g2)("a-select"),d=(0,r.g2)("sdButton"),f=(0,r.g2)("a-col"),v=(0,r.g2)("a-row"),b=(0,r.g2)("sdModal"),m=(0,r.g2)("Wrap");return(0,r.uX)(),(0,r.Wv)(m,null,{default:(0,r.k6)((()=>[(0,r.Lk)("span",null,(0,n.v_)(e.deviceName),1),(0,r.Lk)("span",{class:"text-primary",onClick:t[0]||(t[0]=(...t)=>e.openModal&&e.openModal(...t))}," 選擇裝置"),e.modal?((0,r.uX)(),(0,r.Wv)(b,{key:0,title:"選擇裝置",visible:e.modal,maskClosable:!0,onCancel:e.closeModal},{default:(0,r.k6)((()=>[(0,r.bF)(p,{"label-col":e.labelCol,"wrapper-col":e.wrapperCol,labelAlign:"left"},{default:(0,r.k6)((()=>[(0,r.bF)(s,{label:"地區",style:{"margin-top":"1rem"}},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.regionId,"onUpdate:value":t[1]||(t[1]=t=>e.formState.regionId=t),style:{width:"100%"},"tree-data":e.locations,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data"])])),_:1}),(0,r.bF)(s,{label:"裝置分類"},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.deviceClass,"onUpdate:value":t[2]||(t[2]=t=>e.formState.deviceClass=t),style:{width:"100%"},"tree-data":e.deviceClassOptions,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data"])])),_:1})])),_:1},8,["label-col","wrapper-col"]),(0,r.bF)(c,{value:e.formState.device,"onUpdate:value":t[3]||(t[3]=t=>e.formState.device=t),style:{width:"100%"},options:e.deviceOptions,labelInValue:!0,"show-search":""},null,8,["value","options"]),(0,r.bF)(v,{gutter:[5,10],align:"center",style:{"margin-top":"1rem"}},{default:(0,r.k6)((()=>[(0,r.bF)(f,null,{default:(0,r.k6)((()=>[(0,r.bF)(d,{class:"act-btn",type:"primary",onClick:e.setDevice},{default:(0,r.k6)((()=>[(0,r.eW)(" 選定裝置 ")])),_:1},8,["onClick"])])),_:1}),(0,r.bF)(f,null,{default:(0,r.k6)((()=>[(0,r.bF)(d,{class:"act-btn",type:"light",onClick:e.closeModal},{default:(0,r.k6)((()=>[(0,r.eW)(" 取消 ")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["visible","onCancel"])):(0,r.Q3)("",!0)])),_:1})}a(1532);var p=a(30995),c=(a(18111),a(20116),a(61701),a(95853));const d=c.Ay.div`
   .text-primary{
       color:${({theme:e})=>e["primary-color"]};
       cursor:pointer;
   }
`,f=c.Ay.div`
    border:1px solid ${({theme:e})=>e["primary-color"]};
    border-radius:5px;
    height:300px;
    width:100%;
    overflow:auto;
    margin-top:1rem;
    .device{
        padding:0.5rem;
        font-size:16px;
        cursor:pointer;
        color:${({theme:e})=>e["primary-color"]};
       
    }
    .selected{
        background-color:#ff800010;
        color:${({theme:e})=>e["primary-color"]};
        padding-left:1rem;
    }
`;var v=a(18683),b=(0,r.pM)({props:{value:{type:String,default:null}},components:{DeviceList:f,Wrap:d},setup(e,{emit:t}){const{dispatch:a,state:n}=(0,o.Pj)(),l=(0,u.KR)(!1);(0,r.sV)((async()=>{const e=await a("device/getAllDeviceAndOptions"),t=n.device.deviceInitData.map((e=>({value:e.DeviceId,label:e.DeviceName})));d.value=t,f.value=e.deviceClassOptions,b.value=e.locations}));const i={lg:6,md:9,xs:24},s={lg:18,md:15,xs:24},c=(0,r.EW)((()=>n.device.deviceInitData.find((t=>t.DeviceId===e.value))?.DeviceName)),d=(0,u.KR)([]),f=(0,u.KR)([]),b=(0,u.KR)([]),m=(0,u.Kh)({regionId:null,deviceClass:null,device:{value:null,label:"裝置不存在"}});(0,r.wB)((()=>m),(()=>{const e=[{type:"list",target:m.regionId?m.regionId:null,source:"RegionList",sourceProp:"Id"},{type:"list",target:m.deviceClass?m.deviceClass:null,source:"DeviceCategoryList",sourceProp:"Id"}],t=(0,v.Q8)(e,n.device.deviceInitData).map((e=>({value:e.DeviceId,label:e.DeviceName})));d.value=t}),{deep:!0});const h=(0,u.KR)(!1),g=()=>{m.device=d.value.find((t=>t.value===e.value))??{value:null,label:"裝置不存在"},h.value=!0},y=()=>{m.regionId=null,m.deviceClass=null,m.device.value!==e.value?p.A.confirm({title:"提示",content:"裝置將不會選定，確定關閉？",onOk(){h.value=!1}}):h.value=!1},_=()=>{m.regionId=null,m.deviceClass=null,t("setDevice",m.device),h.value=!1};return{deviceSearching:l,labelCol:i,wrapperCol:s,deviceName:c,deviceOptions:d,deviceClassOptions:f,locations:b,formState:m,modal:h,openModal:g,closeModal:y,setDevice:_}}}),m=a(66262);const h=(0,m.A)(b,[["render",s]]);var g=h;function y(e,t,a,l,o,u){const i=(0,r.g2)("a-tree-select"),s=(0,r.g2)("a-form-item"),p=(0,r.g2)("a-form"),c=(0,r.g2)("a-select"),d=(0,r.g2)("sdButton"),f=(0,r.g2)("a-col"),v=(0,r.g2)("a-row"),b=(0,r.g2)("sdModal"),m=(0,r.g2)("Wrap");return(0,r.uX)(),(0,r.Wv)(m,null,{default:(0,r.k6)((()=>[(0,r.Lk)("span",null,"包含 "+(0,n.v_)(e.values.length)+" 個CCTV",1),(0,r.Lk)("span",{class:"text-primary",onClick:t[0]||(t[0]=(...t)=>e.openModal&&e.openModal(...t))}," 選擇CCTV"),e.modal?((0,r.uX)(),(0,r.Wv)(b,{key:0,title:"選擇CCTV",visible:e.modal,maskClosable:!0,onCancel:e.closeModal},{default:(0,r.k6)((()=>[(0,r.bF)(p,{"label-col":e.labelCol,"wrapper-col":e.wrapperCol,labelAlign:"left"},{default:(0,r.k6)((()=>[(0,r.bF)(s,{label:"地區",style:{"margin-top":"1rem"}},{default:(0,r.k6)((()=>[(0,r.bF)(i,{value:e.formState.regionId,"onUpdate:value":t[1]||(t[1]=t=>e.formState.regionId=t),style:{width:"100%"},"tree-data":e.locations,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data"])])),_:1})])),_:1},8,["label-col","wrapper-col"]),(0,r.bF)(c,{value:e.formState.cctvs,"onUpdate:value":t[2]||(t[2]=t=>e.formState.cctvs=t),mode:"multiple",style:{width:"100%"},options:e.CCTVOptions,labelInValue:!0},null,8,["value","options"]),(0,r.bF)(v,{gutter:[5,10],align:"center",style:{"margin-top":"1rem"}},{default:(0,r.k6)((()=>[(0,r.bF)(f,null,{default:(0,r.k6)((()=>[(0,r.bF)(d,{class:"act-btn",type:"primary",onClick:e.setCCTV},{default:(0,r.k6)((()=>[(0,r.eW)(" 選定CCTV ")])),_:1},8,["onClick"])])),_:1}),(0,r.bF)(f,null,{default:(0,r.k6)((()=>[(0,r.bF)(d,{class:"act-btn",type:"light",onClick:e.closeModal},{default:(0,r.k6)((()=>[(0,r.eW)(" 取消 ")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["visible","onCancel"])):(0,r.Q3)("",!0)])),_:1})}const _=c.Ay.div`
   .text-primary{
       color:${({theme:e})=>e["primary-color"]};
       cursor:pointer;
   }
`;var F=(0,r.pM)({props:{values:{type:Array,default:()=>[]}},components:{Wrap:_},setup(e,{emit:t}){const{dispatch:a,state:n}=(0,o.Pj)();(0,r.sV)((async()=>{const e=await a("cctv/getAllCCTVAndOptions");s.value=e.CCTVList.map((e=>({value:e.Id,label:e.Name}))),c.value=e.locations}));const l={lg:6,md:9,xs:24},i={lg:18,md:15,xs:24},s=(0,u.KR)([]),c=(0,u.KR)([]),d=(0,u.Kh)({regionId:null,cctvs:[]});(0,r.wB)((()=>d),(()=>{const e=[{type:"list",target:d.regionId?d.regionId:null,source:"RegionList",sourceProp:"Id"}],t=(0,v.Q8)(e,n.cctv.initData).map((e=>({value:e.Id,label:e.Name})));s.value=t}),{deep:!0});const f=(0,u.KR)(!1),b=()=>{d.cctvs=e.values.map((e=>s.value.find((t=>t.value===e))??{value:null,label:"CCTV不存在"})),f.value=!0},m=()=>{d.regionId=null,JSON.stringify(d.cctvs.map((e=>e.value)))!==JSON.stringify(e.values)?p.A.confirm({title:"提示",content:"CCTV將不會選定，確定關閉？",onOk(){f.value=!1}}):f.value=!1},h=()=>{d.regionId=null,t("setCCTV",d.cctvs),f.value=!1};return{labelCol:l,wrapperCol:i,CCTVOptions:s,locations:c,formState:d,modal:f,openModal:b,closeModal:m,setCCTV:h}}});const k=(0,m.A)(F,[["render",y]]);var C=k,w=a(31858),S=(0,r.pM)({props:{formState:{type:Object,default:null},typeOptions:{type:Array,default:()=>[]},saveTypeOptions:{type:Array,default:()=>[]},logTypeOptions:{type:Array,default:()=>[]},unitOptions:{type:Array,default:()=>[]},dataTypeOptions:{type:Array,default:()=>[]},locations:{type:Array,default:()=>[]},classOptions:{type:Array,default:()=>[]}},components:{LevelSelect:i.A,DeviceFilter:g,CCTVFilter:C,Notice:w.A},setup(e,{emit:t}){const a=(0,u.KR)("定時:每整點固定分鐘儲存一次 \n &nbsp;&nbsp;&nbsp;&nbsp;例: 設定5分鐘則00:05 00:10開始儲存 \n 循環:即刻開始每固定分鐘儲存一次 \n &nbsp;&nbsp;&nbsp;&nbsp;例:設定5分鐘，現在時間為00:11，則00:1600:21儲存資料"),{state:n}=(0,o.Pj)(),l=(0,r.WQ)("form"),i={region:[{required:!0,message:"請選擇地區",trigger:"blur"}],device:[{required:!0,message:"請選擇裝置",trigger:"blur"}],name:[{required:!0,message:"請輸入名稱",trigger:"blur"}],description:[{required:!0,message:"請輸入說明",trigger:"blur"}],dataType:[{required:!0,message:"請選擇型別",trigger:"blur"}],valueMultiple:[{required:!0,message:"請輸入數值",trigger:"blur"}],valueAddress:[{required:!0,message:"請輸入位址",trigger:"blur"}],transferMax:[{required:!0,message:"請輸入數值",trigger:"blur"}],transferMin:[{required:!0,message:"請輸入數值",trigger:"blur"}],max:[{required:!0,message:"請輸入數值",trigger:"blur"}],min:[{required:!0,message:"請輸入數值",trigger:"blur"}],dataInterval:[{required:!0,message:"請輸入數值",trigger:"blur"}],logInterval:[{required:!0,message:"請輸入數值",trigger:"blur"}],initValue:[{required:!0,message:"請輸入數值",trigger:"blur"}],use:[{required:!0,message:"請選擇用途",trigger:"blur"}],unit:[{required:!0,message:"請選擇單位",trigger:"blur"}]},s=(0,r.EW)((()=>n.tags.loading)),p=[{value:!0,label:"啟用"},{value:!1,label:"停用"}],c=[{value:!0,label:"保持"},{value:!1,label:"不保持"}],d=[{value:!0,label:"儲存"},{value:!1,label:"不儲存"}],f={Normal:"一般點",Alarm:"異常點",Status:"狀態點"},v={NO:"常開點",NC:"常閉點"},b=e=>{t("setCCTV",e)},m=e=>{t("setDevice",e)};return{noticeContent:a,form:l,rules:i,loading:s,statusOptions:p,retentiveOptions:c,logOptions:d,usageOptions:f,closingContactOptions:v,setCCTV:b,setDevice:m}}});const x=(0,m.A)(S,[["render",l]]);var O=x},52814:function(e,t,a){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var n=r(a(5759)),l=function(){function e(e){this.ops=e,this.index=0,this.offset=0}return e.prototype.hasNext=function(){return this.peekLength()<1/0},e.prototype.next=function(e){e||(e=1/0);var t=this.ops[this.index];if(t){var a=this.offset,r=n.default.length(t);if(e>=r-a?(e=r-a,this.index+=1,this.offset=0):this.offset+=e,"number"===typeof t.delete)return{delete:e};var l={};return t.attributes&&(l.attributes=t.attributes),"number"===typeof t.retain?l.retain=e:"string"===typeof t.insert?l.insert=t.insert.substr(a,e):l.insert=t.insert,l}return{retain:1/0}},e.prototype.peek=function(){return this.ops[this.index]},e.prototype.peekLength=function(){return this.ops[this.index]?n.default.length(this.ops[this.index])-this.offset:1/0},e.prototype.peekType=function(){return this.ops[this.index]?"number"===typeof this.ops[this.index].delete?"delete":"number"===typeof this.ops[this.index].retain?"retain":"insert":"retain"},e.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var e=this.offset,t=this.index,a=this.next(),r=this.ops.slice(this.index);return this.offset=e,this.index=t,[a].concat(r)}return[]},e}();t["default"]=l},67193:function(e,t,a){e=a.nmd(e);var r=200,n="__lodash_hash_undefined__",l=9007199254740991,o="[object Arguments]",u="[object Array]",i="[object Boolean]",s="[object Date]",p="[object Error]",c="[object Function]",d="[object GeneratorFunction]",f="[object Map]",v="[object Number]",b="[object Object]",m="[object Promise]",h="[object RegExp]",g="[object Set]",y="[object String]",_="[object Symbol]",F="[object WeakMap]",k="[object ArrayBuffer]",C="[object DataView]",w="[object Float32Array]",S="[object Float64Array]",x="[object Int8Array]",O="[object Int16Array]",j="[object Int32Array]",A="[object Uint8Array]",I="[object Uint8ClampedArray]",T="[object Uint16Array]",L="[object Uint32Array]",M=/[\\^$.*+?()[\]{}|]/g,U=/\w*$/,W=/^\[object .+?Constructor\]$/,E=/^(?:0|[1-9]\d*)$/,N={};N[o]=N[u]=N[k]=N[C]=N[i]=N[s]=N[w]=N[S]=N[x]=N[O]=N[j]=N[f]=N[v]=N[b]=N[h]=N[g]=N[y]=N[_]=N[A]=N[I]=N[T]=N[L]=!0,N[p]=N[c]=N[F]=!1;var V="object"==typeof a.g&&a.g&&a.g.Object===Object&&a.g,X="object"==typeof self&&self&&self.Object===Object&&self,H=V||X||Function("return this")(),D=t&&!t.nodeType&&t,q=D&&e&&!e.nodeType&&e,P=q&&q.exports===D;function R(e,t){return e.set(t[0],t[1]),e}function z(e,t){return e.add(t),e}function K(e,t){var a=-1,r=e?e.length:0;while(++a<r)if(!1===t(e[a],a,e))break;return e}function Q(e,t){var a=-1,r=t.length,n=e.length;while(++a<r)e[n+a]=t[a];return e}function $(e,t,a,r){var n=-1,l=e?e.length:0;r&&l&&(a=e[++n]);while(++n<l)a=t(a,e[n],n,e);return a}function B(e,t){var a=-1,r=Array(e);while(++a<e)r[a]=t(a);return r}function G(e,t){return null==e?void 0:e[t]}function J(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(a){}return t}function Y(e){var t=-1,a=Array(e.size);return e.forEach((function(e,r){a[++t]=[r,e]})),a}function Z(e,t){return function(a){return e(t(a))}}function ee(e){var t=-1,a=Array(e.size);return e.forEach((function(e){a[++t]=e})),a}var te=Array.prototype,ae=Function.prototype,re=Object.prototype,ne=H["__core-js_shared__"],le=function(){var e=/[^.]+$/.exec(ne&&ne.keys&&ne.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),oe=ae.toString,ue=re.hasOwnProperty,ie=re.toString,se=RegExp("^"+oe.call(ue).replace(M,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),pe=P?H.Buffer:void 0,ce=H.Symbol,de=H.Uint8Array,fe=Z(Object.getPrototypeOf,Object),ve=Object.create,be=re.propertyIsEnumerable,me=te.splice,he=Object.getOwnPropertySymbols,ge=pe?pe.isBuffer:void 0,ye=Z(Object.keys,Object),_e=xt(H,"DataView"),Fe=xt(H,"Map"),ke=xt(H,"Promise"),Ce=xt(H,"Set"),we=xt(H,"WeakMap"),Se=xt(Object,"create"),xe=Et(_e),Oe=Et(Fe),je=Et(ke),Ae=Et(Ce),Ie=Et(we),Te=ce?ce.prototype:void 0,Le=Te?Te.valueOf:void 0;function Me(e){var t=-1,a=e?e.length:0;this.clear();while(++t<a){var r=e[t];this.set(r[0],r[1])}}function Ue(){this.__data__=Se?Se(null):{}}function We(e){return this.has(e)&&delete this.__data__[e]}function Ee(e){var t=this.__data__;if(Se){var a=t[e];return a===n?void 0:a}return ue.call(t,e)?t[e]:void 0}function Ne(e){var t=this.__data__;return Se?void 0!==t[e]:ue.call(t,e)}function Ve(e,t){var a=this.__data__;return a[e]=Se&&void 0===t?n:t,this}function Xe(e){var t=-1,a=e?e.length:0;this.clear();while(++t<a){var r=e[t];this.set(r[0],r[1])}}function He(){this.__data__=[]}function De(e){var t=this.__data__,a=lt(t,e);if(a<0)return!1;var r=t.length-1;return a==r?t.pop():me.call(t,a,1),!0}function qe(e){var t=this.__data__,a=lt(t,e);return a<0?void 0:t[a][1]}function Pe(e){return lt(this.__data__,e)>-1}function Re(e,t){var a=this.__data__,r=lt(a,e);return r<0?a.push([e,t]):a[r][1]=t,this}function ze(e){var t=-1,a=e?e.length:0;this.clear();while(++t<a){var r=e[t];this.set(r[0],r[1])}}function Ke(){this.__data__={hash:new Me,map:new(Fe||Xe),string:new Me}}function Qe(e){return St(this,e)["delete"](e)}function $e(e){return St(this,e).get(e)}function Be(e){return St(this,e).has(e)}function Ge(e,t){return St(this,e).set(e,t),this}function Je(e){this.__data__=new Xe(e)}function Ye(){this.__data__=new Xe}function Ze(e){return this.__data__["delete"](e)}function et(e){return this.__data__.get(e)}function tt(e){return this.__data__.has(e)}function at(e,t){var a=this.__data__;if(a instanceof Xe){var n=a.__data__;if(!Fe||n.length<r-1)return n.push([e,t]),this;a=this.__data__=new ze(n)}return a.set(e,t),this}function rt(e,t){var a=Ht(e)||Xt(e)?B(e.length,String):[],r=a.length,n=!!r;for(var l in e)!t&&!ue.call(e,l)||n&&("length"==l||Lt(l,r))||a.push(l);return a}function nt(e,t,a){var r=e[t];ue.call(e,t)&&Vt(r,a)&&(void 0!==a||t in e)||(e[t]=a)}function lt(e,t){var a=e.length;while(a--)if(Vt(e[a][0],t))return a;return-1}function ot(e,t){return e&&kt(t,$t(t),e)}function ut(e,t,a,r,n,l,u){var i;if(r&&(i=l?r(e,n,l,u):r(e)),void 0!==i)return i;if(!Kt(e))return e;var s=Ht(e);if(s){if(i=At(e),!t)return Ft(e,i)}else{var p=jt(e),f=p==c||p==d;if(Pt(e))return ft(e,t);if(p==b||p==o||f&&!l){if(J(e))return l?e:{};if(i=It(f?{}:e),!t)return Ct(e,ot(i,e))}else{if(!N[p])return l?e:{};i=Tt(e,p,ut,t)}}u||(u=new Je);var v=u.get(e);if(v)return v;if(u.set(e,i),!s)var m=a?wt(e):$t(e);return K(m||e,(function(n,l){m&&(l=n,n=e[l]),nt(i,l,ut(n,t,a,r,l,e,u))})),i}function it(e){return Kt(e)?ve(e):{}}function st(e,t,a){var r=t(e);return Ht(e)?r:Q(r,a(e))}function pt(e){return ie.call(e)}function ct(e){if(!Kt(e)||Ut(e))return!1;var t=Rt(e)||J(e)?se:W;return t.test(Et(e))}function dt(e){if(!Wt(e))return ye(e);var t=[];for(var a in Object(e))ue.call(e,a)&&"constructor"!=a&&t.push(a);return t}function ft(e,t){if(t)return e.slice();var a=new e.constructor(e.length);return e.copy(a),a}function vt(e){var t=new e.constructor(e.byteLength);return new de(t).set(new de(e)),t}function bt(e,t){var a=t?vt(e.buffer):e.buffer;return new e.constructor(a,e.byteOffset,e.byteLength)}function mt(e,t,a){var r=t?a(Y(e),!0):Y(e);return $(r,R,new e.constructor)}function ht(e){var t=new e.constructor(e.source,U.exec(e));return t.lastIndex=e.lastIndex,t}function gt(e,t,a){var r=t?a(ee(e),!0):ee(e);return $(r,z,new e.constructor)}function yt(e){return Le?Object(Le.call(e)):{}}function _t(e,t){var a=t?vt(e.buffer):e.buffer;return new e.constructor(a,e.byteOffset,e.length)}function Ft(e,t){var a=-1,r=e.length;t||(t=Array(r));while(++a<r)t[a]=e[a];return t}function kt(e,t,a,r){a||(a={});var n=-1,l=t.length;while(++n<l){var o=t[n],u=r?r(a[o],e[o],o,a,e):void 0;nt(a,o,void 0===u?e[o]:u)}return a}function Ct(e,t){return kt(e,Ot(e),t)}function wt(e){return st(e,$t,Ot)}function St(e,t){var a=e.__data__;return Mt(t)?a["string"==typeof t?"string":"hash"]:a.map}function xt(e,t){var a=G(e,t);return ct(a)?a:void 0}Me.prototype.clear=Ue,Me.prototype["delete"]=We,Me.prototype.get=Ee,Me.prototype.has=Ne,Me.prototype.set=Ve,Xe.prototype.clear=He,Xe.prototype["delete"]=De,Xe.prototype.get=qe,Xe.prototype.has=Pe,Xe.prototype.set=Re,ze.prototype.clear=Ke,ze.prototype["delete"]=Qe,ze.prototype.get=$e,ze.prototype.has=Be,ze.prototype.set=Ge,Je.prototype.clear=Ye,Je.prototype["delete"]=Ze,Je.prototype.get=et,Je.prototype.has=tt,Je.prototype.set=at;var Ot=he?Z(he,Object):Bt,jt=pt;function At(e){var t=e.length,a=e.constructor(t);return t&&"string"==typeof e[0]&&ue.call(e,"index")&&(a.index=e.index,a.input=e.input),a}function It(e){return"function"!=typeof e.constructor||Wt(e)?{}:it(fe(e))}function Tt(e,t,a,r){var n=e.constructor;switch(t){case k:return vt(e);case i:case s:return new n(+e);case C:return bt(e,r);case w:case S:case x:case O:case j:case A:case I:case T:case L:return _t(e,r);case f:return mt(e,r,a);case v:case y:return new n(e);case h:return ht(e);case g:return gt(e,r,a);case _:return yt(e)}}function Lt(e,t){return t=null==t?l:t,!!t&&("number"==typeof e||E.test(e))&&e>-1&&e%1==0&&e<t}function Mt(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}function Ut(e){return!!le&&le in e}function Wt(e){var t=e&&e.constructor,a="function"==typeof t&&t.prototype||re;return e===a}function Et(e){if(null!=e){try{return oe.call(e)}catch(t){}try{return e+""}catch(t){}}return""}function Nt(e){return ut(e,!0,!0)}function Vt(e,t){return e===t||e!==e&&t!==t}function Xt(e){return qt(e)&&ue.call(e,"callee")&&(!be.call(e,"callee")||ie.call(e)==o)}(_e&&jt(new _e(new ArrayBuffer(1)))!=C||Fe&&jt(new Fe)!=f||ke&&jt(ke.resolve())!=m||Ce&&jt(new Ce)!=g||we&&jt(new we)!=F)&&(jt=function(e){var t=ie.call(e),a=t==b?e.constructor:void 0,r=a?Et(a):void 0;if(r)switch(r){case xe:return C;case Oe:return f;case je:return m;case Ae:return g;case Ie:return F}return t});var Ht=Array.isArray;function Dt(e){return null!=e&&zt(e.length)&&!Rt(e)}function qt(e){return Qt(e)&&Dt(e)}var Pt=ge||Gt;function Rt(e){var t=Kt(e)?ie.call(e):"";return t==c||t==d}function zt(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=l}function Kt(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function Qt(e){return!!e&&"object"==typeof e}function $t(e){return Dt(e)?rt(e):dt(e)}function Bt(){return[]}function Gt(){return!1}e.exports=Nt},70982:function(e){var t=-1,a=1,r=0;function n(e,t,a,n){if(e===t)return e?[[r,e]]:[];if(null!=a){var o=g(e,t,a);if(o)return o}var u=i(e,t),p=e.substring(0,u);e=e.substring(u),t=t.substring(u),u=s(e,t);var d=e.substring(e.length-u);e=e.substring(0,e.length-u),t=t.substring(0,t.length-u);var f=l(e,t);return p&&f.unshift([r,p]),d&&f.push([r,d]),c(f,n),f}function l(e,l){var u;if(!e)return[[a,l]];if(!l)return[[t,e]];var i=e.length>l.length?e:l,s=e.length>l.length?l:e,c=i.indexOf(s);if(-1!==c)return u=[[a,i.substring(0,c)],[r,s],[a,i.substring(c+s.length)]],e.length>l.length&&(u[0][0]=u[2][0]=t),u;if(1===s.length)return[[t,e],[a,l]];var d=p(e,l);if(d){var f=d[0],v=d[1],b=d[2],m=d[3],h=d[4],g=n(f,b),y=n(v,m);return g.concat([[r,h]],y)}return o(e,l)}function o(e,r){for(var n=e.length,l=r.length,o=Math.ceil((n+l)/2),i=o,s=2*o,p=new Array(s),c=new Array(s),d=0;d<s;d++)p[d]=-1,c[d]=-1;p[i+1]=0,c[i+1]=0;for(var f=n-l,v=f%2!==0,b=0,m=0,h=0,g=0,y=0;y<o;y++){for(var _=-y+b;_<=y-m;_+=2){var F=i+_;O=_===-y||_!==y&&p[F-1]<p[F+1]?p[F+1]:p[F-1]+1;var k=O-_;while(O<n&&k<l&&e.charAt(O)===r.charAt(k))O++,k++;if(p[F]=O,O>n)m+=2;else if(k>l)b+=2;else if(v){var C=i+f-_;if(C>=0&&C<s&&-1!==c[C]){var w=n-c[C];if(O>=w)return u(e,r,O,k)}}}for(var S=-y+h;S<=y-g;S+=2){C=i+S;w=S===-y||S!==y&&c[C-1]<c[C+1]?c[C+1]:c[C-1]+1;var x=w-S;while(w<n&&x<l&&e.charAt(n-w-1)===r.charAt(l-x-1))w++,x++;if(c[C]=w,w>n)g+=2;else if(x>l)h+=2;else if(!v){F=i+f-S;if(F>=0&&F<s&&-1!==p[F]){var O=p[F];k=i+O-F;if(w=n-w,O>=w)return u(e,r,O,k)}}}}return[[t,e],[a,r]]}function u(e,t,a,r){var l=e.substring(0,a),o=t.substring(0,r),u=e.substring(a),i=t.substring(r),s=n(l,o),p=n(u,i);return s.concat(p)}function i(e,t){if(!e||!t||e.charAt(0)!==t.charAt(0))return 0;var a=0,r=Math.min(e.length,t.length),n=r,l=0;while(a<n)e.substring(l,n)==t.substring(l,n)?(a=n,l=a):r=n,n=Math.floor((r-a)/2+a);return d(e.charCodeAt(n-1))&&n--,n}function s(e,t){if(!e||!t||e.slice(-1)!==t.slice(-1))return 0;var a=0,r=Math.min(e.length,t.length),n=r,l=0;while(a<n)e.substring(e.length-n,e.length-l)==t.substring(t.length-n,t.length-l)?(a=n,l=a):r=n,n=Math.floor((r-a)/2+a);return f(e.charCodeAt(e.length-n))&&n--,n}function p(e,t){var a=e.length>t.length?e:t,r=e.length>t.length?t:e;if(a.length<4||2*r.length<a.length)return null;function n(e,t,a){var r,n,l,o,u=e.substring(a,a+Math.floor(e.length/4)),p=-1,c="";while(-1!==(p=t.indexOf(u,p+1))){var d=i(e.substring(a),t.substring(p)),f=s(e.substring(0,a),t.substring(0,p));c.length<f+d&&(c=t.substring(p-f,p)+t.substring(p,p+d),r=e.substring(0,a-f),n=e.substring(a+d),l=t.substring(0,p-f),o=t.substring(p+d))}return 2*c.length>=e.length?[r,n,l,o,c]:null}var l,o,u,p,c,d=n(a,r,Math.ceil(a.length/4)),f=n(a,r,Math.ceil(a.length/2));if(!d&&!f)return null;l=f?d&&d[4].length>f[4].length?d:f:d,e.length>t.length?(o=l[0],u=l[1],p=l[2],c=l[3]):(p=l[0],c=l[1],o=l[2],u=l[3]);var v=l[4];return[o,u,p,c,v]}function c(e,n){e.push([r,""]);var l,o=0,u=0,p=0,d="",f="";while(o<e.length)if(o<e.length-1&&!e[o][1])e.splice(o,1);else switch(e[o][0]){case a:p++,f+=e[o][1],o++;break;case t:u++,d+=e[o][1],o++;break;case r:var m=o-p-u-1;if(n){if(m>=0&&b(e[m][1])){var h=e[m][1].slice(-1);if(e[m][1]=e[m][1].slice(0,-1),d=h+d,f=h+f,!e[m][1]){e.splice(m,1),o--;var g=m-1;e[g]&&e[g][0]===a&&(p++,f=e[g][1]+f,g--),e[g]&&e[g][0]===t&&(u++,d=e[g][1]+d,g--),m=g}}if(v(e[o][1])){h=e[o][1].charAt(0);e[o][1]=e[o][1].slice(1),d+=h,f+=h}}if(o<e.length-1&&!e[o][1]){e.splice(o,1);break}if(d.length>0||f.length>0){d.length>0&&f.length>0&&(l=i(f,d),0!==l&&(m>=0?e[m][1]+=f.substring(0,l):(e.splice(0,0,[r,f.substring(0,l)]),o++),f=f.substring(l),d=d.substring(l)),l=s(f,d),0!==l&&(e[o][1]=f.substring(f.length-l)+e[o][1],f=f.substring(0,f.length-l),d=d.substring(0,d.length-l)));var y=p+u;0===d.length&&0===f.length?(e.splice(o-y,y),o-=y):0===d.length?(e.splice(o-y,y,[a,f]),o=o-y+1):0===f.length?(e.splice(o-y,y,[t,d]),o=o-y+1):(e.splice(o-y,y,[t,d],[a,f]),o=o-y+2)}0!==o&&e[o-1][0]===r?(e[o-1][1]+=e[o][1],e.splice(o,1)):o++,p=0,u=0,d="",f="";break}""===e[e.length-1][1]&&e.pop();var _=!1;o=1;while(o<e.length-1)e[o-1][0]===r&&e[o+1][0]===r&&(e[o][1].substring(e[o][1].length-e[o-1][1].length)===e[o-1][1]?(e[o][1]=e[o-1][1]+e[o][1].substring(0,e[o][1].length-e[o-1][1].length),e[o+1][1]=e[o-1][1]+e[o+1][1],e.splice(o-1,1),_=!0):e[o][1].substring(0,e[o+1][1].length)==e[o+1][1]&&(e[o-1][1]+=e[o+1][1],e[o][1]=e[o][1].substring(e[o+1][1].length)+e[o+1][1],e.splice(o+1,1),_=!0)),o++;_&&c(e,n)}function d(e){return e>=55296&&e<=56319}function f(e){return e>=56320&&e<=57343}function v(e){return f(e.charCodeAt(0))}function b(e){return d(e.charCodeAt(e.length-1))}function m(e){for(var t=[],a=0;a<e.length;a++)e[a][1].length>0&&t.push(e[a]);return t}function h(e,n,l,o){return b(e)||v(o)?null:m([[r,e],[t,n],[a,l],[r,o]])}function g(e,t,a){var r="number"===typeof a?{index:a,length:0}:a.oldRange,n="number"===typeof a?null:a.newRange,l=e.length,o=t.length;if(0===r.length&&(null===n||0===n.length)){var u=r.index,i=e.slice(0,u),s=e.slice(u),p=n?n.index:null,c=u+o-l;if((null===p||p===c)&&!(c<0||c>o)){var d=t.slice(0,c),f=t.slice(c);if(f===s){var v=Math.min(u,c),b=i.slice(0,v),m=d.slice(0,v);if(b===m){var g=i.slice(v),y=d.slice(v);return h(b,g,y,s)}}}if(null===p||p===u){var _=u;d=t.slice(0,_),f=t.slice(_);if(d===i){var F=Math.min(l-_,o-_),k=s.slice(s.length-F),C=f.slice(f.length-F);if(k===C){g=s.slice(0,s.length-F),y=f.slice(0,f.length-F);return h(i,g,y,k)}}}}if(r.length>0&&n&&0===n.length){b=e.slice(0,r.index),k=e.slice(r.index+r.length),v=b.length,F=k.length;if(!(o<v+F)){m=t.slice(0,v),C=t.slice(o-F);if(b===m&&k===C){g=e.slice(v,l-F),y=t.slice(v,o-F);return h(b,g,y,k)}}}return null}function y(e,t,a){return n(e,t,a,!0)}y.INSERT=a,y.DELETE=t,y.EQUAL=r,e.exports=y},77017:function(e,t,a){"use strict";a.d(t,{A:function(){return h}});var r=a(20641),n=a(72644);function l(e,t,a,l,o,u){const i=(0,r.g2)("a-select-option"),s=(0,r.g2)("a-select"),p=(0,r.g2)("a-form-item"),c=(0,r.g2)("a-col"),d=(0,r.g2)("a-row"),f=(0,r.g2)("a-radio-group"),v=(0,r.g2)("QuillEditor"),b=(0,r.g2)("a-tree-select"),m=(0,r.g2)("a-input"),h=(0,r.g2)("a-time-picker"),g=(0,r.g2)("a-radio"),y=(0,r.g2)("a-form");return(0,r.uX)(),(0,r.Wv)(y,{ref:"form",model:e.alarmFormState,rules:e.alarmRules,labelAlign:"left"},{default:(0,r.k6)((()=>[(0,r.bF)(d,{gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:6},label:"啟用警報",name:"status"},{default:(0,r.k6)((()=>[(0,r.bF)(s,{value:e.alarmFormState.status,"onUpdate:value":t[0]||(t[0]=t=>e.alarmFormState.status=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.statusOptions,(e=>((0,r.uX)(),(0,r.Wv)(i,{value:e.Id,key:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),1!==e.alarmFormState.status?((0,r.uX)(),(0,r.Wv)(d,{key:0,gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:20},label:"播放語音",name:"audio"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.audio,"onUpdate:value":t[1]||(t[1]=t=>e.alarmFormState.audio=t),options:e.exceptionStatusOptions},null,8,["value","options"])])),_:1})])),_:1}),(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:20},label:"SOP",name:"sop"},{default:(0,r.k6)((()=>[(0,r.bF)(v,{content:e.alarmFormState.sop,"onUpdate:content":t[2]||(t[2]=t=>e.alarmFormState.sop=t),contentType:"html",theme:"snow",toolbar:[[{size:[]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],["link","image","video"]]},null,8,["content"])])),_:1})])),_:1}),(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:20},label:"通知群組",name:"notifyGroup"},{default:(0,r.k6)((()=>[(0,r.bF)(s,{value:e.alarmFormState.notifyGroup,"onUpdate:value":t[3]||(t[3]=t=>e.alarmFormState.notifyGroup=t),mode:"multiple",style:{width:"100%"},disabled:0===e.notifyGroupOptions.length},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.notifyGroupOptions,(e=>((0,r.uX)(),(0,r.Wv)(i,{value:e.Id,key:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value","disabled"])])),_:1})])),_:1}),(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:20},label:"關聯頁面",name:"page"},{default:(0,r.k6)((()=>[(0,r.bF)(b,{value:e.alarmFormState.page,"onUpdate:value":t[4]||(t[4]=t=>e.alarmFormState.page=t),style:{width:"100%"},"tree-data":e.pageOptions,"allow-clear":"","field-names":{children:"Children",label:"Name",value:"Id"},placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data"])])),_:1})])),_:1})])),_:1})):(0,r.Q3)("",!0),1!==e.alarmFormState.status&&1===e.type?((0,r.uX)(),(0,r.Wv)(d,{key:1,gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:20},label:"HH狀態",name:"HHStatus"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.HHStatus,"onUpdate:value":t[5]||(t[5]=t=>e.alarmFormState.HHStatus=t),options:e.exceptionStatusOptions},null,8,["value","options"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"HH警報值",name:"HHValue"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.HHValue,"onUpdate:value":t[6]||(t[6]=t=>e.alarmFormState.HHValue=t),placeholder:"0",style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"HH說明",name:"HHContent"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.HHContent,"onUpdate:value":t[7]||(t[7]=t=>e.alarmFormState.HHContent=t),disabled:""===e.alarmFormState.HHValue,style:{height:"40px"}},null,8,["value","disabled"])])),_:1})])),_:1}),(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:20},label:"HI狀態",name:"HIStatus"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.HIStatus,"onUpdate:value":t[8]||(t[8]=t=>e.alarmFormState.HIStatus=t),options:e.exceptionStatusOptions},null,8,["value","options"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"HI警報值",name:"HIValue"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.HIValue,"onUpdate:value":t[9]||(t[9]=t=>e.alarmFormState.HIValue=t),placeholder:"0",style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"HI說明",name:"HIContent"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.HIContent,"onUpdate:value":t[10]||(t[10]=t=>e.alarmFormState.HIContent=t),disabled:""===e.alarmFormState.HIValue,style:{height:"40px"}},null,8,["value","disabled"])])),_:1})])),_:1}),(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:20},label:"LO狀態",name:"LOStatus"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.LOStatus,"onUpdate:value":t[11]||(t[11]=t=>e.alarmFormState.LOStatus=t),options:e.exceptionStatusOptions},null,8,["value","options"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"LO警報值",name:"LOValue"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.LOValue,"onUpdate:value":t[12]||(t[12]=t=>e.alarmFormState.LOValue=t),placeholder:"0",style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"LO說明",name:"LOContent"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.LOContent,"onUpdate:value":t[13]||(t[13]=t=>e.alarmFormState.LOContent=t),disabled:""===e.alarmFormState.LOValue,style:{height:"40px"}},null,8,["value","disabled"])])),_:1})])),_:1}),(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:20},label:"LL狀態",name:"LLStatus"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.LLStatus,"onUpdate:value":t[14]||(t[14]=t=>e.alarmFormState.LLStatus=t),options:e.exceptionStatusOptions},null,8,["value","options"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"LL警報值",name:"LLValue"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.LLValue,"onUpdate:value":t[15]||(t[15]=t=>e.alarmFormState.LLValue=t),placeholder:"0",style:{height:"40px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"LL說明",name:"LLContent"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.LLContent,"onUpdate:value":t[16]||(t[16]=t=>e.alarmFormState.LLContent=t),disabled:""===e.alarmFormState.LLValue,style:{height:"40px"}},null,8,["value","disabled"])])),_:1})])),_:1})])),_:1})):(0,r.Q3)("",!0),1!==e.alarmFormState.status&&0===e.type?((0,r.uX)(),(0,r.Wv)(d,{key:2,gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:8},label:"警報狀態",name:"digAlarmStatus"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.digAlarmStatus,"onUpdate:value":t[17]||(t[17]=t=>e.alarmFormState.digAlarmStatus=t),options:e.exceptionStatusOptions},null,8,["value","options"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[e.alarmFormState.digAlarmStatus?((0,r.uX)(),(0,r.Wv)(p,{key:0,labelCol:{sm:8},wrapperCol:{sm:16},label:"警報值",name:"digAlarmValue"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.digAlarmValue,"onUpdate:value":t[18]||(t[18]=t=>e.alarmFormState.digAlarmValue=t),style:{height:"40px"}},null,8,["value"])])),_:1})):(0,r.Q3)("",!0)])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[e.alarmFormState.digAlarmStatus?((0,r.uX)(),(0,r.Wv)(p,{key:0,labelCol:{sm:8},wrapperCol:{sm:16},label:"警報說明",name:"digAlarmContent"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.digAlarmContent,"onUpdate:value":t[19]||(t[19]=t=>e.alarmFormState.digAlarmContent=t),style:{height:"40px"}},null,8,["value"])])),_:1})):(0,r.Q3)("",!0)])),_:1}),(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:8},label:"復歸狀態",name:"digNorStatus"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.digNorStatus,"onUpdate:value":t[20]||(t[20]=t=>e.alarmFormState.digNorStatus=t),options:e.exceptionStatusOptions},null,8,["value","options"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[e.alarmFormState.digNorStatus?((0,r.uX)(),(0,r.Wv)(p,{key:0,labelCol:{sm:8},wrapperCol:{sm:16},label:"復歸值",name:"digNorValue"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.digNorValue,"onUpdate:value":t[21]||(t[21]=t=>e.alarmFormState.digNorValue=t),style:{height:"40px"}},null,8,["value"])])),_:1})):(0,r.Q3)("",!0)])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[e.alarmFormState.digNorStatus?((0,r.uX)(),(0,r.Wv)(p,{key:0,labelCol:{sm:8},wrapperCol:{sm:16},label:"復歸說明",name:"digNorContent"},{default:(0,r.k6)((()=>[(0,r.bF)(m,{value:e.alarmFormState.digNorContent,"onUpdate:value":t[22]||(t[22]=t=>e.alarmFormState.digNorContent=t),style:{height:"40px"}},null,8,["value"])])),_:1})):(0,r.Q3)("",!0)])),_:1})])),_:1})):(0,r.Q3)("",!0),1!==e.alarmFormState.status?((0,r.uX)(),(0,r.Wv)(d,{key:3},{default:(0,r.k6)((()=>[(0,r.bF)(c,{span:24},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:4},wrapperCol:{sm:20},label:"例外設定",name:"exceptionStatus"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.exceptionStatus,"onUpdate:value":t[23]||(t[23]=t=>e.alarmFormState.exceptionStatus=t),options:e.exceptionStatusOptions},null,8,["value","options"])])),_:1})])),_:1})])),_:1})):(0,r.Q3)("",!0),1!==e.alarmFormState.status&&e.alarmFormState.exceptionStatus?((0,r.uX)(),(0,r.Wv)(d,{key:4,gutter:10},{default:(0,r.k6)((()=>[(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"開始時間",name:"exceptionStartAt"},{default:(0,r.k6)((()=>[(0,r.bF)(h,{value:e.alarmFormState.exceptionStartAt,"onUpdate:value":t[24]||(t[24]=t=>e.alarmFormState.exceptionStartAt=t),style:{"min-width":"150px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:5},wrapperCol:{sm:19},label:"至",name:"exceptionUntil"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.exceptionUntil,"onUpdate:value":t[25]||(t[25]=t=>e.alarmFormState.exceptionUntil=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.exceptionUntilOptions,(e=>((0,r.uX)(),(0,r.Wv)(g,{key:e.id,value:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:8},wrapperCol:{sm:16},label:"結束時間",name:"exceptionEndAt"},{default:(0,r.k6)((()=>[(0,r.bF)(h,{value:e.alarmFormState.exceptionEndAt,"onUpdate:value":t[26]||(t[26]=t=>e.alarmFormState.exceptionEndAt=t),style:{"min-width":"150px"}},null,8,["value"])])),_:1})])),_:1}),(0,r.bF)(c,{span:12},{default:(0,r.k6)((()=>[(0,r.bF)(p,{labelCol:{sm:5},wrapperCol:{sm:19},label:"動作",name:"exceptionAction"},{default:(0,r.k6)((()=>[(0,r.bF)(f,{value:e.alarmFormState.exceptionAction,"onUpdate:value":t[27]||(t[27]=t=>e.alarmFormState.exceptionAction=t)},{default:(0,r.k6)((()=>[((0,r.uX)(!0),(0,r.CE)(r.FK,null,(0,r.pI)(e.exceptionActionOptions,(e=>((0,r.uX)(),(0,r.Wv)(g,{key:e.id,value:e.Id},{default:(0,r.k6)((()=>[(0,r.eW)((0,n.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})])),_:1})):(0,r.Q3)("",!0)])),_:1},8,["model","rules"])}a(18111),a(61701);var o=a(40834),u=a(79841),i=a(91574),s=a.n(i),p=a(12660),c=a.n(p);
/*!
 * VueQuill @vueup/vue-quill v1.2.0
 * https://vueup.github.io/vue-quill/
 * 
 * Includes quill v1.3.7
 * https://quilljs.com/
 * 
 * Copyright (c) 2023 Ahmad Luthfi Masruri
 * Released under the MIT license
 * Date: 2023-05-12T08:44:03.742Z
 */
const d={essential:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}],["blockquote","code-block","link"],[{color:[]},"clean"]],minimal:[[{header:1},{header:2}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}]],full:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["link","video","image"],["clean"]]},f=(0,r.pM)({name:"QuillEditor",inheritAttrs:!1,props:{content:{type:[String,Object]},contentType:{type:String,default:"delta",validator:e=>["delta","html","text"].includes(e)},enable:{type:Boolean,default:!0},readOnly:{type:Boolean,default:!1},placeholder:{type:String,required:!1},theme:{type:String,default:"snow",validator:e=>["snow","bubble",""].includes(e)},toolbar:{type:[String,Array,Object],required:!1,validator:e=>"string"!==typeof e||""===e||("#"===e.charAt(0)||-1!==Object.keys(d).indexOf(e))},modules:{type:Object,required:!1},options:{type:Object,required:!1},globalOptions:{type:Object,required:!1}},emits:["textChange","selectionChange","editorChange","update:content","focus","blur","ready"],setup:(e,t)=>{let a,n;(0,r.sV)((()=>{o()})),(0,r.xo)((()=>{a=null}));const l=(0,u.KR)(),o=()=>{var r;if(l.value){if(n=i(),e.modules)if(Array.isArray(e.modules))for(const t of e.modules)s().register(`modules/${t.name}`,t.module);else s().register(`modules/${e.modules.name}`,e.modules.module);a=new(s())(l.value,n),w(e.content),a.on("text-change",m),a.on("selection-change",g),a.on("editor-change",y),"bubble"!==e.theme&&l.value.classList.remove("ql-bubble"),"snow"!==e.theme&&l.value.classList.remove("ql-snow"),null===(r=a.getModule("toolbar"))||void 0===r||r.container.addEventListener("mousedown",(e=>{e.preventDefault()})),t.emit("ready",a)}},i=()=>{const t={};if(""!==e.theme&&(t.theme=e.theme),e.readOnly&&(t.readOnly=e.readOnly),e.placeholder&&(t.placeholder=e.placeholder),e.toolbar&&""!==e.toolbar&&(t.modules={toolbar:(()=>{if("object"===typeof e.toolbar)return e.toolbar;if("string"===typeof e.toolbar){const t=e.toolbar;return"#"===t.charAt(0)?e.toolbar:d[e.toolbar]}})()}),e.modules){const a=(()=>{var t,a;const r={};if(Array.isArray(e.modules))for(const n of e.modules)r[n.name]=null!==(t=n.options)&&void 0!==t?t:{};else r[e.modules.name]=null!==(a=e.modules.options)&&void 0!==a?a:{};return r})();t.modules=Object.assign({},t.modules,a)}return Object.assign({},e.globalOptions,e.options,t)},p=e=>"object"===typeof e&&e?e.slice():e,f=e=>Object.values(e.ops).some((e=>!e.retain||1!==Object.keys(e).length));let v;const b=e=>{if(typeof v===typeof e){if(e===v)return!0;if("object"===typeof e&&e&&"object"===typeof v&&v)return!f(v.diff(e))}return!1},m=(a,r,n)=>{v=p(C()),b(e.content)||t.emit("update:content",v),t.emit("textChange",{delta:a,oldContents:r,source:n})},h=(0,u.KR)(),g=(e,r,n)=>{h.value=!!(null===a||void 0===a?void 0:a.hasFocus()),t.emit("selectionChange",{range:e,oldRange:r,source:n})};(0,r.wB)(h,(e=>{e?t.emit("focus",l):t.emit("blur",l)}));const y=(...e)=>{"text-change"===e[0]&&t.emit("editorChange",{name:e[0],delta:e[1],oldContents:e[2],source:e[3]}),"selection-change"===e[0]&&t.emit("editorChange",{name:e[0],range:e[1],oldRange:e[2],source:e[3]})},_=()=>l.value,F=()=>{var e;return null===(e=null===a||void 0===a?void 0:a.getModule("toolbar"))||void 0===e?void 0:e.container},k=()=>{if(a)return a;throw'The quill editor hasn\'t been instantiated yet,\n                  make sure to call this method when the editor ready\n                  or use v-on:ready="onReady(quill)" event instead.'},C=(t,r)=>"html"===e.contentType?O():"text"===e.contentType?S(t,r):null===a||void 0===a?void 0:a.getContents(t,r),w=(t,r="api")=>{const n=t||("delta"===e.contentType?new(c()):"");"html"===e.contentType?j(n):"text"===e.contentType?x(n,r):null===a||void 0===a||a.setContents(n,r),v=p(n)},S=(e,t)=>{var r;return null!==(r=null===a||void 0===a?void 0:a.getText(e,t))&&void 0!==r?r:""},x=(e,t="api")=>{null===a||void 0===a||a.setText(e,t)},O=()=>{var e;return null!==(e=null===a||void 0===a?void 0:a.root.innerHTML)&&void 0!==e?e:""},j=e=>{a&&(a.root.innerHTML=e)},A=(e,t="api")=>{const r=null===a||void 0===a?void 0:a.clipboard.convert(e);r&&(null===a||void 0===a||a.setContents(r,t))},I=()=>{null===a||void 0===a||a.focus()},T=()=>{(0,r.dY)((()=>{var e;!t.slots.toolbar&&a&&(null===(e=a.getModule("toolbar"))||void 0===e||e.container.remove()),o()}))};return(0,r.wB)((()=>e.content),(e=>{if(!a||!e||b(e))return;const t=a.getSelection();t&&(0,r.dY)((()=>null===a||void 0===a?void 0:a.setSelection(t))),w(e)}),{deep:!0}),(0,r.wB)((()=>e.enable),(e=>{a&&a.enable(e)})),{editor:l,getEditor:_,getToolbar:F,getQuill:k,getContents:C,setContents:w,getHTML:O,setHTML:j,pasteHTML:A,focus:I,getText:S,setText:x,reinit:T}},render(){var e,t;return[null===(t=(e=this.$slots).toolbar)||void 0===t?void 0:t.call(e),(0,r.h)("div",{ref:"editor",...this.$attrs})]}});var v=(0,r.pM)({props:{alarmFormState:{type:Object,default:null},type:{type:Number,default:1},statusOptions:{type:Array,default:()=>[]},exceptionUntilOptions:{type:Array,default:()=>[]},exceptionActionOptions:{type:Array,default:()=>[]},digitalAlarmValueOptions:{type:Array,default:()=>[]}},components:{QuillEditor:f},setup(){const{dispatch:e,state:t}=(0,o.Pj)(),a=(0,r.WQ)("alarmForm"),n={content:[{required:!0,message:"請輸入說明",trigger:"blur"}],exceptionStartAt:[{required:!0,message:"請選擇",trigger:"blur"}],exceptionEndAt:[{required:!0,message:"請選擇",trigger:"blur"}],exceptionUntil:[{required:!0,message:"請選擇",trigger:"blur"}],exceptionAction:[{required:!0,message:"請選擇",trigger:"blur"}]},l=(0,u.EW)((()=>t.notify.groupInitData)),i=[{value:!0,label:"啟用"},{value:!1,label:"停用"}];(0,r.sV)((async()=>{e("notify/getGroupsAndOptions");const t=await e("gui/getAllPages"),a=e=>e.map((e=>{const t={...e};return 2===e.Category&&(t.disabled=!0),e.Children&&(t.Children=a(e.Children)),t}));s.value=a(t.data)}));const s=(0,u.KR)([]);return{form:a,alarmRules:n,notifyGroupOptions:l,exceptionStatusOptions:i,pageOptions:s}}}),b=a(66262);const m=(0,b.A)(v,[["render",l]]);var h=m},82077:function(e,t,a){"use strict";a.d(t,{M:function(){return n}});var r=a(92317);function n(e,t){const a=(0,r.Gq)("tagList");if(!a)return null;const n=a[e];return n?n[t]:null}},99106:function(e,t,a){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var n,l=r(a(67193)),o=r(a(8142));(function(e){function t(e,t,a){void 0===e&&(e={}),void 0===t&&(t={}),"object"!==typeof e&&(e={}),"object"!==typeof t&&(t={});var r=l.default(t);for(var n in a||(r=Object.keys(r).reduce((function(e,t){return null!=r[t]&&(e[t]=r[t]),e}),{})),e)void 0!==e[n]&&void 0===t[n]&&(r[n]=e[n]);return Object.keys(r).length>0?r:void 0}function a(e,t){void 0===e&&(e={}),void 0===t&&(t={}),"object"!==typeof e&&(e={}),"object"!==typeof t&&(t={});var a=Object.keys(e).concat(Object.keys(t)).reduce((function(a,r){return o.default(e[r],t[r])||(a[r]=void 0===t[r]?null:t[r]),a}),{});return Object.keys(a).length>0?a:void 0}function r(e,t){void 0===e&&(e={}),void 0===t&&(t={}),e=e||{};var a=Object.keys(t).reduce((function(a,r){return t[r]!==e[r]&&void 0!==e[r]&&(a[r]=t[r]),a}),{});return Object.keys(e).reduce((function(a,r){return e[r]!==t[r]&&void 0===t[r]&&(a[r]=null),a}),a)}function n(e,t,a){if(void 0===a&&(a=!1),"object"!==typeof e)return t;if("object"===typeof t){if(!a)return t;var r=Object.keys(t).reduce((function(a,r){return void 0===e[r]&&(a[r]=t[r]),a}),{});return Object.keys(r).length>0?r:void 0}}e.compose=t,e.diff=a,e.invert=r,e.transform=n})(n||(n={})),t["default"]=n}}]);