<template>
  <div class="base-chart-container">
    <!-- 圖表標題和工具列 -->
    <div v-if="$slots.header || title || $slots.toolbar" class="chart-header mb-4">
      <div class="flex items-center justify-between">
        <!-- 標題區域 -->
        <div v-if="$slots.header || title" class="flex items-center space-x-3">
          <div v-if="icon" class="flex-shrink-0">
            <n-icon :size="20" :color="iconColor">
              <component :is="icon" />
            </n-icon>
          </div>
          <div>
            <h3 v-if="title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>
            <p v-if="subtitle" class="text-sm text-gray-500 mt-1">{{ subtitle }}</p>
          </div>
          <slot name="header" />
        </div>
        
        <!-- 工具列區域 -->
        <div v-if="$slots.toolbar" class="flex items-center space-x-3">
          <slot name="toolbar" />
        </div>
      </div>
    </div>
    
    <!-- 圖表容器 -->
    <div :class="chartContainerClasses">
      <div 
        ref="chartRef" 
        :style="{ height: height, width: width }"
        class="chart-wrapper"
      ></div>
      
      <!-- 載入狀態 -->
      <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
        <n-spin size="large" />
      </div>
      
      <!-- 空狀態 -->
      <div v-if="!loading && isEmpty" class="absolute inset-0 flex items-center justify-center">
        <div class="text-center">
          <n-icon size="48" color="#d1d5db">
            <component :is="emptyIcon" />
          </n-icon>
          <p class="text-gray-500 mt-2">{{ emptyText }}</p>
        </div>
      </div>
    </div>
    
    <!-- 圖表底部資訊 -->
    <div v-if="showLegend || $slots.footer" class="chart-footer mt-4">
      <!-- 圖例 -->
      <div v-if="showLegend && legendData.length" class="flex flex-wrap gap-4 mb-2">
        <div 
          v-for="(item, index) in legendData" 
          :key="index"
          class="flex items-center space-x-2"
        >
          <div 
            class="w-3 h-3 rounded-full"
            :style="{ backgroundColor: item.color }"
          ></div>
          <span class="text-sm text-gray-600">{{ item.name }}</span>
        </div>
      </div>
      
      <!-- 自定義底部插槽 -->
      <slot name="footer" />
    </div>
  </div>
</template>

<script>
import { computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { NIcon, NSpin } from 'naive-ui'
import { BarChartOutline } from '@vicons/ionicons5'

export default {
  name: 'BaseChart',
  components: {
    NIcon,
    NSpin
  },
  props: {
    // 基本屬性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: Object,
      default: null
    },
    iconColor: {
      type: String,
      default: '#f97316'
    },
    
    // 圖表配置
    options: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      default: 'line',
      validator: value => ['line', 'bar', 'pie', 'area', 'scatter', 'radar'].includes(value)
    },
    
    // 尺寸
    height: {
      type: String,
      default: '400px'
    },
    width: {
      type: String,
      default: '100%'
    },
    
    // 狀態
    loading: {
      type: Boolean,
      default: false
    },
    isEmpty: {
      type: Boolean,
      default: false
    },
    
    // 空狀態
    emptyText: {
      type: String,
      default: '暫無數據'
    },
    emptyIcon: {
      type: Object,
      default: () => BarChartOutline
    },
    
    // 圖例
    showLegend: {
      type: Boolean,
      default: false
    },
    legendData: {
      type: Array,
      default: () => []
    },
    
    // 樣式
    variant: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'secondary', 'success', 'warning', 'error', 'info'].includes(value)
    },
    bordered: {
      type: Boolean,
      default: true
    },
    shadow: {
      type: Boolean,
      default: true
    },
    
    // 響應式
    responsive: {
      type: Boolean,
      default: true
    },
    
    // 動畫
    animation: {
      type: Boolean,
      default: true
    }
  },
  emits: ['chart-ready', 'chart-click', 'chart-hover', 'chart-resize'],
  setup(props, { emit }) {
    const chartRef = ref(null)
    const chartInstance = ref(null)
    
    // 計算樣式類別
    const chartContainerClasses = computed(() => {
      const classes = ['relative', 'chart-container']
      
      if (props.bordered) {
        classes.push('border', 'border-gray-200', 'rounded-lg')
      }
      
      if (props.shadow) {
        classes.push('shadow-sm')
      }
      
      // 變體樣式
      switch (props.variant) {
        case 'primary':
          classes.push('border-primary-200', 'bg-primary-50')
          break
        case 'secondary':
          classes.push('border-secondary-200', 'bg-secondary-50')
          break
        case 'success':
          classes.push('border-green-200', 'bg-green-50')
          break
        case 'warning':
          classes.push('border-yellow-200', 'bg-yellow-50')
          break
        case 'error':
          classes.push('border-red-200', 'bg-red-50')
          break
        case 'info':
          classes.push('border-blue-200', 'bg-blue-50')
          break
        default:
          classes.push('bg-white')
      }
      
      return classes
    })
    
    // 初始化圖表
    const initChart = async () => {
      if (!chartRef.value) return
      
      try {
        // 這裡可以集成不同的圖表庫，如 ApexCharts, Chart.js, ECharts 等
        // 目前使用 ApexCharts 作為範例
        const ApexCharts = await import('apexcharts')
        
        if (chartInstance.value) {
          chartInstance.value.destroy()
        }
        
        const chartOptions = {
          ...props.options,
          chart: {
            ...props.options.chart,
            type: props.type,
            animations: {
              enabled: props.animation
            },
            events: {
              click: (event, chartContext, config) => {
                emit('chart-click', { event, chartContext, config })
              },
              mouseMove: (event, chartContext, config) => {
                emit('chart-hover', { event, chartContext, config })
              }
            }
          },
          responsive: props.responsive ? [
            {
              breakpoint: 768,
              options: {
                chart: {
                  height: 300
                },
                legend: {
                  position: 'bottom'
                }
              }
            }
          ] : []
        }
        
        chartInstance.value = new ApexCharts.default(chartRef.value, chartOptions)
        await chartInstance.value.render()
        
        emit('chart-ready', chartInstance.value)
      } catch (error) {
        console.error('圖表初始化失敗:', error)
      }
    }
    
    // 更新圖表
    const updateChart = async () => {
      if (chartInstance.value && props.options) {
        try {
          await chartInstance.value.updateOptions(props.options, true, props.animation)
        } catch (error) {
          console.error('圖表更新失敗:', error)
        }
      }
    }
    
    // 調整圖表大小
    const resizeChart = () => {
      if (chartInstance.value) {
        chartInstance.value.resize()
        emit('chart-resize')
      }
    }
    
    // 監聽選項變化
    watch(() => props.options, updateChart, { deep: true })
    
    // 響應式處理
    let resizeObserver = null
    
    onMounted(async () => {
      await nextTick()
      await initChart()
      
      if (props.responsive && window.ResizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          resizeChart()
        })
        resizeObserver.observe(chartRef.value)
      }
    })
    
    onUnmounted(() => {
      if (chartInstance.value) {
        chartInstance.value.destroy()
      }
      if (resizeObserver) {
        resizeObserver.disconnect()
      }
    })
    
    // 暴露方法
    const exportChart = (format = 'png') => {
      if (chartInstance.value) {
        return chartInstance.value.dataURI({ format })
      }
    }
    
    const downloadChart = (filename = 'chart') => {
      if (chartInstance.value) {
        chartInstance.value.downloadSVG(filename)
      }
    }
    
    return {
      chartRef,
      chartContainerClasses,
      exportChart,
      downloadChart,
      resizeChart
    }
  }
}
</script>

<style scoped>
.chart-container {
  @apply p-4;
}

.chart-wrapper {
  @apply w-full;
}

.chart-header {
  @apply border-b border-gray-200 pb-4;
}

.chart-footer {
  @apply border-t border-gray-200 pt-4;
}

/* 深色模式支援 */
.dark .chart-container {
  @apply bg-gray-800 border-gray-700;
}

.dark .chart-header {
  @apply border-gray-700;
}

.dark .chart-footer {
  @apply border-gray-700;
}
</style>
