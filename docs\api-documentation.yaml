openapi: 3.0.3
info:
  title: PLC 能源管理系統 API
  description: |
    PLC 2.0 能源管理系統的完整 API 文檔
    
    ## 功能概述
    - 即時數據監控
    - 歷史數據查詢
    - 設備管理
    - 告警系統
    - 用戶權限管理
    - 報表生成
    
    ## 認證方式
    使用 JWT Token 進行身份驗證，需在請求標頭中包含：
    ```
    Authorization: Bearer <token>
    ```
    
  version: 2.0.0
  contact:
    name: PLC 開發團隊
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.plc.ococom.tw/v2
    description: 生產環境
  - url: https://staging-api.plc.ococom.tw/v2
    description: 測試環境
  - url: http://localhost:8080/api/v2
    description: 開發環境

security:
  - bearerAuth: []

paths:
  # 認證相關
  /auth/login:
    post:
      tags:
        - 認證
      summary: 用戶登入
      description: 使用帳號密碼進行登入驗證
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: 用戶名稱
                password:
                  type: string
                  format: password
                  description: 密碼
      responses:
        '200':
          description: 登入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT Token
                  user:
                    $ref: '#/components/schemas/User'
        '401':
          description: 認證失敗

  # 即時數據
  /realtime/tags:
    get:
      tags:
        - 即時數據
      summary: 獲取即時標籤數據
      description: 獲取指定標籤的即時數值
      parameters:
        - name: tagIds
          in: query
          description: 標籤ID列表，用逗號分隔
          required: true
          schema:
            type: string
            example: "tag1,tag2,tag3"
        - name: format
          in: query
          description: 數據格式
          schema:
            type: string
            enum: [json, csv]
            default: json
      responses:
        '200':
          description: 成功獲取數據
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RealtimeData'

  # 歷史數據
  /history/tags:
    get:
      tags:
        - 歷史數據
      summary: 獲取歷史數據
      description: 查詢指定時間範圍內的歷史數據
      parameters:
        - name: tagIds
          in: query
          required: true
          schema:
            type: string
        - name: startTime
          in: query
          required: true
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          required: true
          schema:
            type: string
            format: date-time
        - name: interval
          in: query
          description: 數據間隔（秒）
          schema:
            type: integer
            default: 60
      responses:
        '200':
          description: 成功獲取歷史數據
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HistoryData'

  # 設備管理
  /devices:
    get:
      tags:
        - 設備管理
      summary: 獲取設備列表
      responses:
        '200':
          description: 設備列表
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Device'
    post:
      tags:
        - 設備管理
      summary: 新增設備
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceInput'
      responses:
        '201':
          description: 設備創建成功

  /devices/{deviceId}:
    get:
      tags:
        - 設備管理
      summary: 獲取設備詳情
      parameters:
        - name: deviceId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 設備詳情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'

  # 告警系統
  /alarms:
    get:
      tags:
        - 告警系統
      summary: 獲取告警列表
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, resolved, all]
            default: all
        - name: severity
          in: query
          schema:
            type: string
            enum: [low, medium, high, critical]
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 告警列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Alarm'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        username:
          type: string
        email:
          type: string
        role:
          type: string
          enum: [admin, operator, viewer]
        permissions:
          type: array
          items:
            type: string

    RealtimeData:
      type: object
      properties:
        tagId:
          type: string
          description: 標籤ID
        value:
          type: number
          description: 數值
        quality:
          type: string
          enum: [good, bad, uncertain]
          description: 數據品質
        timestamp:
          type: string
          format: date-time
          description: 時間戳

    HistoryData:
      type: object
      properties:
        tagId:
          type: string
        values:
          type: array
          items:
            type: object
            properties:
              value:
                type: number
              timestamp:
                type: string
                format: date-time

    Device:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        type:
          type: string
        status:
          type: string
          enum: [online, offline, error]
        location:
          type: string
        tags:
          type: array
          items:
            type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    DeviceInput:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
        type:
          type: string
        location:
          type: string
        description:
          type: string

    Alarm:
      type: object
      properties:
        id:
          type: string
        message:
          type: string
        severity:
          type: string
          enum: [low, medium, high, critical]
        status:
          type: string
          enum: [active, acknowledged, resolved]
        deviceId:
          type: string
        tagId:
          type: string
        timestamp:
          type: string
          format: date-time
        acknowledgedBy:
          type: string
        acknowledgedAt:
          type: string
          format: date-time

    Pagination:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        totalPages:
          type: integer

    Error:
      type: object
      properties:
        code:
          type: string
        message:
          type: string
        details:
          type: object

tags:
  - name: 認證
    description: 用戶認證相關 API
  - name: 即時數據
    description: 即時數據監控 API
  - name: 歷史數據
    description: 歷史數據查詢 API
  - name: 設備管理
    description: 設備管理相關 API
  - name: 告警系統
    description: 告警系統相關 API
