<template>
  <n-button
    :class="buttonClasses"
    :type="naiveType"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :ghost="ghost"
    :dashed="dashed"
    :circle="circle"
    :round="round"
    :icon-placement="iconPlacement"
    :render-icon="renderIcon"
    :tag="tag"
    :block="block"
    :focusable="focusable"
    :keyboard="keyboard"
    :strong="strong"
    :secondary="secondary"
    :tertiary="tertiary"
    :quaternary="quaternary"
    :text="text"
    v-bind="$attrs"
    @click="handleClick"
  >
    <!-- 圖示插槽 -->
    <template #icon v-if="icon && !renderIcon">
      <n-icon :size="iconSize">
        <component :is="icon" />
      </n-icon>
    </template>
    
    <!-- 預設插槽 -->
    <slot />
  </n-button>
</template>

<script>
import { computed } from 'vue'
import { NButton, NIcon } from 'naive-ui'

export default {
  name: 'BaseButton',
  components: {
    NButton,
    NIcon
  },
  inheritAttrs: false,
  emits: ['click'],
  props: {
    // 基本屬性
    variant: {
      type: String,
      default: 'default',
      validator: (value) => [
        'default', 'primary', 'secondary', 'success', 
        'warning', 'error', 'info', 'text', 'link'
      ].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['tiny', 'small', 'medium', 'large'].includes(value)
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 樣式變體
    ghost: {
      type: Boolean,
      default: false
    },
    dashed: {
      type: Boolean,
      default: false
    },
    circle: {
      type: Boolean,
      default: false
    },
    round: {
      type: Boolean,
      default: false
    },
    block: {
      type: Boolean,
      default: false
    },
    strong: {
      type: Boolean,
      default: false
    },
    secondary: {
      type: Boolean,
      default: false
    },
    tertiary: {
      type: Boolean,
      default: false
    },
    quaternary: {
      type: Boolean,
      default: false
    },
    text: {
      type: Boolean,
      default: false
    },
    
    // 圖示相關
    icon: {
      type: [String, Object],
      default: null
    },
    iconSize: {
      type: [String, Number],
      default: 16
    },
    iconPlacement: {
      type: String,
      default: 'left',
      validator: (value) => ['left', 'right'].includes(value)
    },
    renderIcon: {
      type: Function,
      default: null
    },
    
    // 其他屬性
    tag: {
      type: String,
      default: 'button'
    },
    focusable: {
      type: Boolean,
      default: true
    },
    keyboard: {
      type: Boolean,
      default: true
    },
    
    // 自定義樣式
    shadow: {
      type: String,
      default: 'none',
      validator: (value) => ['none', 'sm', 'md', 'lg'].includes(value)
    },
    rounded: {
      type: String,
      default: 'lg',
      validator: (value) => ['none', 'sm', 'md', 'lg', 'xl', '2xl', 'full'].includes(value)
    }
  },
  
  setup(props, { emit }) {
    // 計算 Naive UI 的 type 屬性
    const naiveType = computed(() => {
      switch (props.variant) {
        case 'primary':
          return 'primary'
        case 'secondary':
          return 'default'
        case 'success':
          return 'success'
        case 'warning':
          return 'warning'
        case 'error':
          return 'error'
        case 'info':
          return 'info'
        case 'text':
          return 'default'
        case 'link':
          return 'default'
        default:
          return 'default'
      }
    })
    
    // 計算按鈕樣式類別
    const buttonClasses = computed(() => {
      const classes = ['base-button']
      
      // 變體樣式
      classes.push(`btn-${props.variant}`)
      
      // 陰影
      if (props.shadow !== 'none') {
        classes.push(`shadow-${props.shadow}`)
      }
      
      // 圓角
      if (props.rounded !== 'none') {
        classes.push(`rounded-${props.rounded}`)
      }
      
      // 特殊樣式
      if (props.text) {
        classes.push('btn-text')
      }
      
      if (props.ghost) {
        classes.push('btn-ghost')
      }
      
      if (props.dashed) {
        classes.push('btn-dashed')
      }
      
      return classes
    })
    
    // 處理點擊事件
    const handleClick = (event) => {
      if (!props.disabled && !props.loading) {
        emit('click', event)
      }
    }
    
    return {
      naiveType,
      buttonClasses,
      handleClick
    }
  }
}
</script>

<style scoped>
.base-button {
  @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

/* 變體樣式 */
.btn-default {
  @apply bg-white border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-primary {
  @apply bg-primary-500 border-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
}

.btn-secondary {
  @apply bg-secondary-500 border-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500;
}

.btn-success {
  @apply bg-success-500 border-success-500 text-white hover:bg-success-600 focus:ring-success-500;
}

.btn-warning {
  @apply bg-warning-500 border-warning-500 text-white hover:bg-warning-600 focus:ring-warning-500;
}

.btn-error {
  @apply bg-error-500 border-error-500 text-white hover:bg-error-600 focus:ring-error-500;
}

.btn-info {
  @apply bg-blue-500 border-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500;
}

.btn-text {
  @apply bg-transparent border-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-500;
}

.btn-link {
  @apply bg-transparent border-transparent text-primary-600 hover:text-primary-700 hover:underline focus:ring-primary-500;
}

/* Ghost 樣式 */
.btn-ghost.btn-primary {
  @apply bg-transparent text-primary-600 hover:bg-primary-50;
}

.btn-ghost.btn-secondary {
  @apply bg-transparent text-secondary-600 hover:bg-secondary-50;
}

.btn-ghost.btn-success {
  @apply bg-transparent text-success-600 hover:bg-success-50;
}

.btn-ghost.btn-warning {
  @apply bg-transparent text-warning-600 hover:bg-warning-50;
}

.btn-ghost.btn-error {
  @apply bg-transparent text-error-600 hover:bg-error-50;
}

.btn-ghost.btn-info {
  @apply bg-transparent text-blue-600 hover:bg-blue-50;
}

/* Dashed 樣式 */
.btn-dashed {
  @apply border-dashed;
}

/* 深色模式支援 */
.dark .btn-default {
  @apply bg-gray-700 border-gray-600 text-gray-200 hover:bg-gray-600;
}

.dark .btn-text {
  @apply text-gray-300 hover:text-gray-100 hover:bg-gray-700;
}

.dark .btn-link {
  @apply text-primary-400 hover:text-primary-300;
}

/* 禁用狀態 */
.base-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 載入狀態 */
.base-button.loading {
  @apply cursor-wait;
}
</style>
