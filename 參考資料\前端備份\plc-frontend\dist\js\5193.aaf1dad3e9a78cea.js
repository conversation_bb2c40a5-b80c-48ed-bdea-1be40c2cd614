"use strict";(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[5193],{45118:function(e,a,t){t.d(a,{A:function(){return f}});var l=t(20641),n=t(72644);const i={class:"ninjadash-datatable-filter"},r={key:0,class:"ninjadash-datatable-filter__right"},o={class:"ninjadasj-datatable"};function s(e,a,t,s,d,p){const c=(0,l.g2)("sdButton"),u=(0,l.g2)("a-space"),m=(0,l.g2)("unicon"),g=(0,l.g2)("a-input"),h=(0,l.g2)("a-button"),y=(0,l.g2)("a-col"),f=(0,l.g2)("a-row"),b=(0,l.g2)("a-table"),k=(0,l.g2)("TableWrapper"),C=(0,l.g2)("DataTableStyleWrap");return(0,l.uX)(),(0,l.Wv)(C,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",i,[(0,l.Lk)("div",null,[(0,l.bF)(u,null,{default:(0,l.k6)((()=>[e.addOption?((0,l.uX)(),(0,l.Wv)(c,{key:0,class:"act-btn",type:"primary",onClick:e.handleAdd},{default:(0,l.k6)((()=>[(0,l.eW)(" 新增 ")])),_:1},8,["onClick"])):(0,l.Q3)("",!0),e.backOption?((0,l.uX)(),(0,l.Wv)(c,{key:1,size:"default",outlined:!0,type:"primary",onClick:e.handleBack},{default:(0,l.k6)((()=>[(0,l.eW)(" 回上層 "+(0,n.v_)(e.backTitle),1)])),_:1},8,["onClick"])):(0,l.Q3)("",!0)])),_:1})]),e.filterOption?((0,l.uX)(),(0,l.CE)("div",r,[(0,l.bF)(g,{onChange:e.handleDataSearch,size:"default",placeholder:"搜尋"},{prefix:(0,l.k6)((()=>[(0,l.bF)(m,{name:"search"})])),_:1},8,["onChange"])])):(0,l.Q3)("",!0)]),(0,l.bF)(f,{align:"end"},{default:(0,l.k6)((()=>[e.importOption?((0,l.uX)(),(0,l.Wv)(y,{key:0,style:{"margin-bottom":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(h,{type:"primary",ghost:"",onClick:e.handleImport},{default:(0,l.k6)((()=>[(0,l.eW)("匯入")])),_:1},8,["onClick"])])),_:1})):(0,l.Q3)("",!0),e.exportOption?((0,l.uX)(),(0,l.Wv)(y,{key:1,style:{"margin-bottom":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(h,{type:"primary",ghost:"",onClick:e.handleExport},{default:(0,l.k6)((()=>[(0,l.eW)("匯出Excel")])),_:1},8,["onClick"])])),_:1})):(0,l.Q3)("",!0)])),_:1}),(0,l.Lk)("div",o,[(0,l.bF)(k,{class:"table-data-view table-responsive"},{default:(0,l.k6)((()=>[e.rowSelection?((0,l.uX)(),(0,l.Wv)(b,{key:0,class:"ant-table-striped","row-selection":e.rowSelections,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},childrenColumnName:e.childrenColumnName,"row-class-name":e.getRowClassName,"data-source":e.tableData,columns:e.columns,onChange:e.changePageSize},null,8,["row-selection","pagination","childrenColumnName","row-class-name","data-source","columns","onChange"])):((0,l.uX)(),(0,l.Wv)(b,{key:1,pagination:{pageSize:e.pageSize,pageSizeOptions:e.pageSizeOptions,showSizeChanger:!0},class:"ant-table-striped",childrenColumnName:e.childrenColumnName,"data-source":e.tableData,"row-class-name":e.getRowClassName,columns:e.columns,onChange:e.changePageSize},null,8,["pagination","childrenColumnName","data-source","row-class-name","columns","onChange"]))])),_:1})])])),_:1})}var d=t(79841),p=t(19732),c=t(95853);const u=c.Ay.div`
    .ninjadash-datatable-filter{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin: 20px 0 25px 0;
        @media only screen and (max-width: 575px){
            flex-wrap: wrap;
        }
        .ninjadash-datatable-filter__left{
            display: inline-flex;
            width: 100%;
            align-items: center;
            .ant-form{
                display: inline-flex;
                width: 100%;
                align-items: center;
            }
            span.label{
                margin-right: 8px;
                color: ${({theme:e})=>e[e.mainContent]["gray-text"]};
            }
            .ninjadash-datatable-filter__input{
                display: flex;
                align-items: center;
                padding-right: 20px;
                .ant-input{
                    height: 40px;
                }
            }
        }
        .ninjadash-datatable-filter__right{
            min-width: 280px;
            @media only screen and (max-width: 575px){
                margin-top: 15px;
            }
            .ant-input-affix-wrapper{
                padding: 7.22px 20px;
                border-radius: 6px;
                .ant-input-prefix{
                    svg{
                        width: 16px;
                        height: 16px;
                        fill: ${({theme:e})=>e[e.mainContent]["light-text"]};
                    }
                }
            }
        }
    }
`;var m=t(79570),g=(0,l.pM)({components:{DataTableStyleWrap:u,TableWrapper:m.AC},props:{filterOption:p.Ay.bool,filterOnchange:p.Ay.bool,rowSelection:p.Ay.bool,defaultSelected:p.Ay.array,tableData:p.Ay.array,columns:p.Ay.array,handleDataSearch:p.Ay.func,handleAdd:p.Ay.func,handleBack:p.Ay.func,handleImport:p.Ay.func,handleExport:p.Ay.func,rowClassFunc:{type:Function,default:()=>{}},backOption:{type:Boolean,default:!1},addOption:{type:Boolean,default:!1},exportOption:{type:Boolean,default:!1},importOption:{type:Boolean,default:!1},expandedRow:{type:Object,default:null},childrenColumnName:{type:String,default:"children"},backTitle:{type:String,default:""}},setup(e,{emit:a}){const t=(0,d.KR)([]);(0,l.wB)((()=>e.defaultSelected),(e=>{t.value=e}),{immediate:!0});const n=e=>{t.value=e,a("onSelectChange",t.value)},i=(0,l.EW)((()=>({selectedRowKeys:(0,d.R1)(t),onChange:n,hideDefaultSelections:!0}))),r=(0,d.KR)(10),o=(0,d.KR)(["10","20","50","100"]),s=e=>{r.value=e.pageSize},p=({record:a})=>e.expandedRow.innerDataProp?a[e.expandedRow.innerDataProp]:a.children,c=(a,t)=>e.rowClassFunc(a)??t%2===1?"table-striped row-style":"row-style";return{pageSize:r,pageSizeOptions:o,rowSelections:i,changePageSize:s,getInnerData:p,getRowClassName:c}}}),h=t(66262);const y=(0,h.A)(g,[["render",s],["__scopeId","data-v-1b881e36"]]);var f=y},45193:function(e,a,t){t.r(a),t.d(a,{default:function(){return k}});t(18111),t(61701);var l=t(20641),n=t(72644);function i(e,a,t,i,r,o){const s=(0,l.g2)("sdPageHeader"),d=(0,l.g2)("TagFilter"),p=(0,l.g2)("a-form-item"),c=(0,l.g2)("a-range-picker"),u=(0,l.g2)("PeriodSelect"),m=(0,l.g2)("a-form"),g=(0,l.g2)("a-spin"),h=(0,l.g2)("sdButton"),y=(0,l.g2)("sdCards"),f=(0,l.g2)("DataTables"),b=(0,l.g2)("Main");return(0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(s,{title:"歷史警報",class:"ninjadash-page-header-main",routes:[{breadcrumbName:"警報系統"},{breadcrumbName:"歷史警報"}]}),(0,l.bF)(b,null,{default:(0,l.k6)((()=>[(0,l.bF)(y,null,{default:(0,l.k6)((()=>[(0,l.bF)(m,{model:e.formState,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,labelAlign:"left"},{default:(0,l.k6)((()=>[1===e.formState.searchType?((0,l.uX)(),(0,l.Wv)(p,{key:0,label:"測點列表"},{default:(0,l.k6)((()=>[(0,l.bF)(d,{selectedTags:e.formState.tags.map((e=>e.id)),onSetTags:e.setTags},null,8,["selectedTags","onSetTags"])])),_:1})):(0,l.Q3)("",!0),(0,l.bF)(p,{label:"時間"},{default:(0,l.k6)((()=>[(0,l.bF)(c,{value:e.formState.date,"onUpdate:value":a[0]||(a[0]=a=>e.formState.date=a),bordered:!1},null,8,["value"]),(0,l.bF)(u,{onSetDate:e.setDate},null,8,["onSetDate"])])),_:1})])),_:1},8,["model","label-col","wrapper-col"]),(0,l.bF)(h,{class:"act-btn",type:"primary",disabled:!e.sumitable,onClick:e.submit},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(e.searching?"查詢中..":"查詢")+" ",1),e.searching?((0,l.uX)(),(0,l.Wv)(g,{key:0,size:"small"})):(0,l.Q3)("",!0)])),_:1},8,["disabled","onClick"])])),_:1}),(0,l.bF)(y,null,{default:(0,l.k6)((()=>[e.searching?((0,l.uX)(),(0,l.Wv)(g,{key:0})):(0,l.Q3)("",!0),e.showTable?((0,l.uX)(),(0,l.Wv)(f,{key:1,filterOption:!0,filterOnchange:!0,tableData:e.tableData,columns:e.columns,rowSelection:!1,handleDataSearch:e.search},null,8,["tableData","columns","handleDataSearch"])):(0,l.Q3)("",!0)])),_:1})])),_:1})])}t(1532);var r=t(30995),o=t(79570),s=t(79841),d=t(45118),p=t(40834),c=t(82046),u=t(59835),m=t(74353),g=t.n(m),h=t(95804),y=(0,l.pM)({components:{Main:o.gZ,DataTables:d.A,GroupFilter:u.A,TagFilter:c.A,PeriodSelect:h.A},setup(){const{state:e,dispatch:a}=(0,p.Pj)(),t=(0,l.EW)((()=>e.alarm.loading));(0,l.sV)((async()=>{const e=await a("alarm/getHistoryOptions");o.value=e.searchType}));const n={lg:2,md:9,xs:24},i={lg:5,md:15,xs:24},o=(0,s.KR)([]),d=(0,s.Kh)({searchType:1,tags:[],groups:[],date:null}),c=({startTime:e,endTime:a})=>{d.date=[g()(e),g()(a)]};(0,l.wB)((()=>d.searchType),(()=>{d.tags=[],d.groups=[]}));const u=e=>{d.tags=e},m=e=>{d.groups=e},h=(0,l.EW)((()=>!f.value)),y=async()=>{try{b.value=!1,f.value=!0,await a("alarm/fetchAlarmHistory",d),f.value=!1,b.value=!0}catch(e){f.value=!1,r.A.error({title:"發生錯誤",content:e.message})}},f=(0,s.KR)(!1),b=(0,s.KR)(!1),k=[{title:"時間",dataIndex:"AlarmTime",key:"AlarmTime"},{title:"測點",dataIndex:"FullTagName",key:"FullTagName"},{title:"測點說明",dataIndex:"TagDescription",key:"TagDescription"},{title:"測點類型",dataIndex:"TagTypeText",key:"TagTypeText"},{title:"警報狀態",dataIndex:"AlarmStateText",key:"AlarmStateText"},{title:"警報等級",dataIndex:"AlarmPriorityText",key:"AlarmPriorityText"},{title:"測點值",dataIndex:"TagValue",key:"TagValue",align:"right"}],C=(0,l.EW)((()=>e.alarm.historyTableData.map((e=>({...e,AlarmTime:g()(e.AlarmTime).format("YYYY-MM-DD HH:mm:ss")}))))),T=e=>{a("alarm/filterAlarmHistory",e.target.value)};return{loading:t,labelCol:n,wrapperCol:i,searchTypeOptions:o,formState:d,setDate:c,setTags:u,setGroups:m,submit:y,sumitable:h,searching:f,showTable:b,columns:k,tableData:C,search:T}}}),f=t(66262);const b=(0,f.A)(y,[["render",i]]);var k=b},95804:function(e,a,t){t.d(a,{A:function(){return o}});var l=t(20641),n=t(10984),i={__name:"Index",props:{noToday:{type:Boolean,default:!1}},emits:["setDate"],setup(e,{emit:a}){const t=e=>{const{startTime:t,endTime:l}=(0,n.I)(e);a("setDate",{startTime:t,endTime:l})};return(a,n)=>{const i=(0,l.g2)("a-button"),r=(0,l.g2)("a-space");return(0,l.uX)(),(0,l.Wv)(r,{style:{"margin-top":"0.5rem"}},{default:(0,l.k6)((()=>[e.noToday?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.Wv)(i,{key:0,type:"primary",ghost:"",onClick:n[0]||(n[0]=e=>t(1))},{default:(0,l.k6)((()=>[(0,l.eW)("今日")])),_:1})),(0,l.bF)(i,{type:"primary",ghost:"",onClick:n[1]||(n[1]=e=>t(2))},{default:(0,l.k6)((()=>[(0,l.eW)("昨日")])),_:1}),(0,l.bF)(i,{type:"primary",ghost:"",onClick:n[2]||(n[2]=e=>t(3))},{default:(0,l.k6)((()=>[(0,l.eW)("本週")])),_:1}),(0,l.bF)(i,{type:"primary",ghost:"",onClick:n[3]||(n[3]=e=>t(4))},{default:(0,l.k6)((()=>[(0,l.eW)("上週")])),_:1}),(0,l.bF)(i,{type:"primary",ghost:"",onClick:n[4]||(n[4]=e=>t(5))},{default:(0,l.k6)((()=>[(0,l.eW)("本月")])),_:1}),(0,l.bF)(i,{type:"primary",ghost:"",onClick:n[5]||(n[5]=e=>t(6))},{default:(0,l.k6)((()=>[(0,l.eW)("上月")])),_:1}),(0,l.bF)(i,{type:"primary",ghost:"",onClick:n[6]||(n[6]=e=>t(7))},{default:(0,l.k6)((()=>[(0,l.eW)("今年")])),_:1}),(0,l.bF)(i,{type:"primary",ghost:"",onClick:n[7]||(n[7]=e=>t(8))},{default:(0,l.k6)((()=>[(0,l.eW)("去年")])),_:1})])),_:1})}}};const r=i;var o=r}}]);