<template>
  <div class="ui-showcase p-6 bg-gray-50 min-h-screen">
    <!-- 頁面標題 -->
    <div class="mb-8">
      <h1 class="text-4xl font-bold text-gray-900 mb-2">新 UI 設計系統展示</h1>
      <p class="text-lg text-gray-600">基於 Naive UI + Tailwind CSS 的現代化設計系統</p>
    </div>

    <!-- 色彩系統展示 -->
    <BaseCard title="色彩系統" class="mb-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- 主要色彩 -->
        <div>
          <h4 class="text-lg font-semibold mb-3">主要色彩 (Primary)</h4>
          <div class="space-y-2">
            <div v-for="shade in [50, 100, 200, 300, 400, 500, 600, 700, 800, 900]" 
                 :key="shade" 
                 :class="`bg-primary-${shade} h-10 rounded-lg flex items-center px-3`"
                 :style="{ backgroundColor: `var(--color-primary-${shade})` }">
              <span :class="shade >= 500 ? 'text-white' : 'text-gray-900'" class="text-sm font-medium">
                primary-{{ shade }}
              </span>
            </div>
          </div>
        </div>

        <!-- 次要色彩 -->
        <div>
          <h4 class="text-lg font-semibold mb-3">次要色彩 (Secondary)</h4>
          <div class="space-y-2">
            <div v-for="shade in [50, 100, 200, 300, 400, 500, 600, 700, 800, 900]" 
                 :key="shade" 
                 :class="`bg-secondary-${shade} h-10 rounded-lg flex items-center px-3`"
                 :style="{ backgroundColor: `var(--color-secondary-${shade})` }">
              <span :class="shade >= 500 ? 'text-white' : 'text-gray-900'" class="text-sm font-medium">
                secondary-{{ shade }}
              </span>
            </div>
          </div>
        </div>

        <!-- 功能色彩 -->
        <div>
          <h4 class="text-lg font-semibold mb-3">功能色彩</h4>
          <div class="space-y-2">
            <div class="bg-success-500 h-10 rounded-lg flex items-center px-3">
              <span class="text-white text-sm font-medium">Success</span>
            </div>
            <div class="bg-warning-500 h-10 rounded-lg flex items-center px-3">
              <span class="text-white text-sm font-medium">Warning</span>
            </div>
            <div class="bg-error-500 h-10 rounded-lg flex items-center px-3">
              <span class="text-white text-sm font-medium">Error</span>
            </div>
            <div class="bg-blue-500 h-10 rounded-lg flex items-center px-3">
              <span class="text-white text-sm font-medium">Info</span>
            </div>
          </div>
        </div>
      </div>
    </BaseCard>

    <!-- 按鈕組件展示 -->
    <BaseCard title="按鈕組件" class="mb-8">
      <div class="space-y-6">
        <!-- 基本按鈕 -->
        <div>
          <h4 class="text-lg font-semibold mb-3">基本按鈕</h4>
          <div class="flex flex-wrap gap-3">
            <BaseButton variant="default">預設按鈕</BaseButton>
            <BaseButton variant="primary">主要按鈕</BaseButton>
            <BaseButton variant="secondary">次要按鈕</BaseButton>
            <BaseButton variant="success">成功按鈕</BaseButton>
            <BaseButton variant="warning">警告按鈕</BaseButton>
            <BaseButton variant="error">錯誤按鈕</BaseButton>
            <BaseButton variant="info">資訊按鈕</BaseButton>
          </div>
        </div>

        <!-- Ghost 按鈕 -->
        <div>
          <h4 class="text-lg font-semibold mb-3">Ghost 按鈕</h4>
          <div class="flex flex-wrap gap-3">
            <BaseButton variant="primary" ghost>主要 Ghost</BaseButton>
            <BaseButton variant="secondary" ghost>次要 Ghost</BaseButton>
            <BaseButton variant="success" ghost>成功 Ghost</BaseButton>
            <BaseButton variant="warning" ghost>警告 Ghost</BaseButton>
            <BaseButton variant="error" ghost>錯誤 Ghost</BaseButton>
          </div>
        </div>

        <!-- 按鈕尺寸 -->
        <div>
          <h4 class="text-lg font-semibold mb-3">按鈕尺寸</h4>
          <div class="flex flex-wrap items-center gap-3">
            <BaseButton variant="primary" size="tiny">極小</BaseButton>
            <BaseButton variant="primary" size="small">小</BaseButton>
            <BaseButton variant="primary" size="medium">中等</BaseButton>
            <BaseButton variant="primary" size="large">大</BaseButton>
          </div>
        </div>

        <!-- 載入和禁用狀態 -->
        <div>
          <h4 class="text-lg font-semibold mb-3">狀態</h4>
          <div class="flex flex-wrap gap-3">
            <BaseButton variant="primary" loading>載入中</BaseButton>
            <BaseButton variant="primary" disabled>禁用</BaseButton>
            <BaseButton variant="primary" round>圓角</BaseButton>
            <BaseButton variant="primary" circle>圓</BaseButton>
          </div>
        </div>
      </div>
    </BaseCard>

    <!-- 卡片組件展示 -->
    <BaseCard title="卡片組件" class="mb-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- 基本卡片 -->
        <BaseCard title="基本卡片" subtitle="這是一個基本卡片的範例">
          <p class="text-gray-600">這裡是卡片的內容區域。可以放置任何內容，包括文字、圖片、表格等。</p>
          <template #footer>
            <div class="flex justify-end space-x-2">
              <BaseButton size="small">取消</BaseButton>
              <BaseButton variant="primary" size="small">確認</BaseButton>
            </div>
          </template>
        </BaseCard>

        <!-- 主要色彩卡片 -->
        <BaseCard title="主要色彩卡片" subtitle="使用主要色彩的卡片" variant="primary">
          <p class="text-gray-600">這是一個使用主要色彩的卡片範例。標題會使用主要色彩，背景會有淡淡的主要色彩。</p>
        </BaseCard>

        <!-- 成功色彩卡片 -->
        <BaseCard title="成功色彩卡片" subtitle="使用成功色彩的卡片" variant="success">
          <p class="text-gray-600">這是一個使用成功色彩的卡片範例。通常用於顯示成功狀態或正面資訊。</p>
        </BaseCard>
      </div>
    </BaseCard>

    <!-- 表格組件展示 -->
    <BaseCard title="表格組件" class="mb-8">
      <BaseTable
        title="用戶資料表"
        subtitle="展示用戶基本資訊"
        :columns="tableColumns"
        :data="tableData"
        :pagination="tablePagination"
        show-summary
      >
        <template #toolbar>
          <BaseButton variant="primary" size="small">新增用戶</BaseButton>
          <BaseButton size="small">匯出</BaseButton>
        </template>
        
        <template #status="{ row }">
          <n-tag :type="row.status === 'active' ? 'success' : 'warning'">
            {{ row.status === 'active' ? '啟用' : '停用' }}
          </n-tag>
        </template>
        
        <template #actions>
          <div class="flex space-x-2">
            <BaseButton size="tiny" variant="primary">編輯</BaseButton>
            <BaseButton size="tiny" variant="error">刪除</BaseButton>
          </div>
        </template>
      </BaseTable>
    </BaseCard>

    <!-- 字體系統展示 -->
    <BaseCard title="字體系統" class="mb-8">
      <div class="space-y-4">
        <div>
          <h1 class="text-4xl font-bold text-gray-900">H1 標題 - 4xl/Bold</h1>
          <p class="text-sm text-gray-500">用於頁面主標題</p>
        </div>
        <div>
          <h2 class="text-3xl font-semibold text-gray-900">H2 標題 - 3xl/Semibold</h2>
          <p class="text-sm text-gray-500">用於區塊標題</p>
        </div>
        <div>
          <h3 class="text-2xl font-semibold text-gray-900">H3 標題 - 2xl/Semibold</h3>
          <p class="text-sm text-gray-500">用於子區塊標題</p>
        </div>
        <div>
          <h4 class="text-xl font-medium text-gray-900">H4 標題 - xl/Medium</h4>
          <p class="text-sm text-gray-500">用於小標題</p>
        </div>
        <div>
          <p class="text-base text-gray-900">正文 - base/Regular</p>
          <p class="text-sm text-gray-500">用於一般內容文字</p>
        </div>
        <div>
          <p class="text-sm text-gray-600">小字 - sm/Regular</p>
          <p class="text-xs text-gray-500">用於輔助說明文字</p>
        </div>
      </div>
    </BaseCard>

    <!-- 間距系統展示 -->
    <BaseCard title="間距系統" class="mb-8">
      <div class="space-y-4">
        <div class="flex items-center space-x-4">
          <div class="w-16 text-sm text-gray-600">1 (4px)</div>
          <div class="bg-primary-500 h-4" style="width: 4px;"></div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="w-16 text-sm text-gray-600">2 (8px)</div>
          <div class="bg-primary-500 h-4" style="width: 8px;"></div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="w-16 text-sm text-gray-600">4 (16px)</div>
          <div class="bg-primary-500 h-4" style="width: 16px;"></div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="w-16 text-sm text-gray-600">6 (24px)</div>
          <div class="bg-primary-500 h-4" style="width: 24px;"></div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="w-16 text-sm text-gray-600">8 (32px)</div>
          <div class="bg-primary-500 h-4" style="width: 32px;"></div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="w-16 text-sm text-gray-600">12 (48px)</div>
          <div class="bg-primary-500 h-4" style="width: 48px;"></div>
        </div>
      </div>
    </BaseCard>
  </div>
</template>

<script>
import { ref } from 'vue'
import { NTag } from 'naive-ui'

export default {
  name: 'UIShowcase',
  components: {
    NTag
  },
  setup() {
    // 表格欄位定義
    const tableColumns = ref([
      {
        title: 'ID',
        key: 'id',
        width: 80
      },
      {
        title: '姓名',
        key: 'name',
        width: 120
      },
      {
        title: '電子郵件',
        key: 'email',
        width: 200
      },
      {
        title: '角色',
        key: 'role',
        width: 100
      },
      {
        title: '狀態',
        key: 'status',
        width: 100
      },
      {
        title: '操作',
        key: 'actions',
        width: 120
      }
    ])

    // 表格資料
    const tableData = ref([
      {
        id: 1,
        name: '張三',
        email: '<EMAIL>',
        role: '管理員',
        status: 'active'
      },
      {
        id: 2,
        name: '李四',
        email: '<EMAIL>',
        role: '用戶',
        status: 'active'
      },
      {
        id: 3,
        name: '王五',
        email: '<EMAIL>',
        role: '用戶',
        status: 'inactive'
      },
      {
        id: 4,
        name: '趙六',
        email: '<EMAIL>',
        role: '編輯',
        status: 'active'
      },
      {
        id: 5,
        name: '錢七',
        email: '<EMAIL>',
        role: '用戶',
        status: 'inactive'
      }
    ])

    // 分頁配置
    const tablePagination = ref({
      page: 1,
      pageSize: 10,
      showSizePicker: true,
      pageSizes: [5, 10, 20, 50],
      showQuickJumper: true
    })

    return {
      tableColumns,
      tableData,
      tablePagination
    }
  }
}
</script>

<style scoped>
.ui-showcase {
  font-family: 'Noto Sans TC', 'Inter', 'Roboto', system-ui, sans-serif;
}
</style>
