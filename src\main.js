import app from "./config/configApp";
import router from "./routes/protectedRoute";
import store from "@/vuex/store";

// 新的 UI 框架和樣式
import "./assets/styles/tailwind.css";
import "./static/css/style.css";

// Naive UI 配置
import {
  create,
  NButton,
  NCard,
  NDataTable,
  NInput,
  NIcon,
  NSpin,
  NSpace,
  NGrid,
  NGridItem,
  NLayout,
  NLayoutHeader,
  NLayoutSider,
  NLayoutContent,
  NLayoutFooter,
  NMenu,
  NDropdown,
  NAvatar,
  NBadge,
  NTag,
  NTooltip,
  NPopover,
  NModal,
  NDrawer,
  NMessage,
  NNotification,
  NLoadingBar,
  NConfigProvider
} from 'naive-ui';

// Vue 3rd party plugins
import "@/core/plugins/ant-design";
import "@/core/plugins/fonts";
import "@/core/plugins/maps";
import "@/core/plugins/masonry";
import "@/core/plugins/apexcharts";
import "@/core/plugins/unicons";
import "@/core/components/custom";
import "@/core/components/style";
import "@/i18n/config";

// 權限指令
import { permission } from "@/directives/permission";

// 基礎元件
import { registerBaseComponents } from "@/components/base";

// 創建 Naive UI 實例
const naive = create({
  components: [
    NButton,
    NCard,
    NDataTable,
    NInput,
    NIcon,
    NSpin,
    NSpace,
    NGrid,
    NGridItem,
    NLayout,
    NLayoutHeader,
    NLayoutSider,
    NLayoutContent,
    NLayoutFooter,
    NMenu,
    NDropdown,
    NAvatar,
    NBadge,
    NTag,
    NTooltip,
    NPopover,
    NModal,
    NDrawer,
    NMessage,
    NNotification,
    NLoadingBar,
    NConfigProvider
  ]
});

app.config.productionTip = false;

// 註冊 Naive UI
app.use(naive);

// 註冊基礎元件
registerBaseComponents(app);

// 註冊權限指令
app.directive('permission', permission);

// 註冊 Vuex store 和 Vue Router
app.use(store);
app.use(router);

// 掛載應用
app.mount("#app", true);

console.log('API ENDPOINT:', process.env.VUE_APP_API_ENDPOINT);