<template>
  <div class="base-table-container">
    <!-- 表格標題和工具列 -->
    <div v-if="$slots.header || title || $slots.toolbar" class="table-header mb-4">
      <div class="flex items-center justify-between">
        <!-- 標題區域 -->
        <div v-if="$slots.header || title" class="flex items-center space-x-3">
          <div v-if="icon" class="flex-shrink-0">
            <n-icon :size="20" :color="iconColor">
              <component :is="icon" />
            </n-icon>
          </div>
          <div>
            <h3 v-if="title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>
            <p v-if="subtitle" class="text-sm text-gray-500 mt-1">{{ subtitle }}</p>
          </div>
          <slot name="header" />
        </div>
        
        <!-- 工具列區域 -->
        <div v-if="$slots.toolbar" class="flex items-center space-x-3">
          <slot name="toolbar" />
        </div>
      </div>
    </div>
    
    <!-- 表格容器 -->
    <div :class="tableContainerClasses">
      <n-data-table
        :class="tableClasses"
        :columns="computedColumns"
        :data="data"
        :loading="loading"
        :pagination="computedPagination"
        :row-key="rowKey"
        :max-height="maxHeight"
        :min-height="minHeight"
        :size="size"
        :bordered="bordered"
        :bottom-bordered="bottomBordered"
        :single-line="singleLine"
        :single-column="singleColumn"
        :striped="striped"
        :scroll-x="scrollX"
        :scroll-y="scrollY"
        :flex-height="flexHeight"
        :summary="summary"
        :remote="remote"
        :default-checked-row-keys="defaultCheckedRowKeys"
        :checked-row-keys="checkedRowKeys"
        :default-expanded-row-keys="defaultExpandedRowKeys"
        :expanded-row-keys="expandedRowKeys"
        :cascade="cascade"
        :children-key="childrenKey"
        :indent="indent"
        :table-layout="tableLayout"
        :allow-checking-not-loaded="allowCheckingNotLoaded"
        v-bind="$attrs"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        @update:expanded-row-keys="handleExpandedRowKeysChange"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:sorter="handleSorterChange"
        @update:filters="handleFiltersChange"
      >
        <!-- 空狀態插槽 -->
        <template #empty v-if="$slots.empty">
          <slot name="empty" />
        </template>
        
        <!-- 載入插槽 -->
        <template #loading v-if="$slots.loading">
          <slot name="loading" />
        </template>
        
        <!-- 動態列插槽 -->
        <template v-for="column in dynamicSlots" :key="column.key" #[column.key]="slotProps">
          <slot :name="column.key" v-bind="slotProps" />
        </template>
      </n-data-table>
    </div>
    
    <!-- 表格底部資訊 -->
    <div v-if="showSummary || $slots.footer" class="table-footer mt-4">
      <!-- 摘要資訊 -->
      <div v-if="showSummary" class="flex items-center justify-between text-sm text-gray-500 mb-2">
        <div>
          共 {{ totalItems }} 項，已選擇 {{ selectedCount }} 項
        </div>
        <div v-if="pagination">
          第 {{ currentPage }} 頁，共 {{ totalPages }} 頁
        </div>
      </div>

      <slot name="footer" />
    </div>
  </div>
</template>

<script>
import { computed, ref } from 'vue'
import { NDataTable, NIcon } from 'naive-ui'

export default {
  name: 'BaseTable',
  components: {
    NDataTable,
    NIcon
  },
  inheritAttrs: false,
  emits: [
    'update:checked-row-keys',
    'update:expanded-row-keys',
    'update:page',
    'update:page-size',
    'update:sorter',
    'update:filters',
    'row-click',
    'row-dblclick'
  ],
  props: {
    // 基本屬性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: [String, Object],
      default: null
    },
    iconColor: {
      type: String,
      default: ''
    },
    
    // 表格數據
    columns: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    
    // 分頁配置
    pagination: {
      type: [Boolean, Object],
      default: false
    },
    
    // 表格配置
    rowKey: {
      type: [String, Function],
      default: 'id'
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    bordered: {
      type: Boolean,
      default: true
    },
    bottomBordered: {
      type: Boolean,
      default: true
    },
    singleLine: {
      type: Boolean,
      default: true
    },
    singleColumn: {
      type: Boolean,
      default: false
    },
    striped: {
      type: Boolean,
      default: false
    },
    
    // 滾動配置
    scrollX: {
      type: [Number, String],
      default: undefined
    },
    scrollY: {
      type: [Number, String],
      default: undefined
    },
    maxHeight: {
      type: [Number, String],
      default: undefined
    },
    minHeight: {
      type: [Number, String],
      default: undefined
    },
    flexHeight: {
      type: Boolean,
      default: false
    },
    
    // 其他配置
    summary: {
      type: Function,
      default: undefined
    },
    remote: {
      type: Boolean,
      default: false
    },
    
    // 選擇配置
    defaultCheckedRowKeys: {
      type: Array,
      default: () => []
    },
    checkedRowKeys: {
      type: Array,
      default: undefined
    },
    
    // 展開配置
    defaultExpandedRowKeys: {
      type: Array,
      default: () => []
    },
    expandedRowKeys: {
      type: Array,
      default: undefined
    },
    
    // 樹形配置
    cascade: {
      type: Boolean,
      default: true
    },
    childrenKey: {
      type: String,
      default: 'children'
    },
    indent: {
      type: Number,
      default: 16
    },
    
    // 佈局配置
    tableLayout: {
      type: String,
      default: 'auto',
      validator: (value) => ['auto', 'fixed'].includes(value)
    },
    allowCheckingNotLoaded: {
      type: Boolean,
      default: false
    },
    
    // 樣式配置
    variant: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'simple', 'bordered', 'striped'].includes(value)
    },
    shadow: {
      type: String,
      default: 'md',
      validator: (value) => ['none', 'sm', 'md', 'lg', 'xl'].includes(value)
    },
    rounded: {
      type: String,
      default: '2xl',
      validator: (value) => ['none', 'sm', 'md', 'lg', 'xl', '2xl', '3xl'].includes(value)
    },
    
    // 顯示配置
    showSummary: {
      type: Boolean,
      default: false
    }
  },
  
  setup(props, { emit, slots }) {
    // 內部狀態
    const currentPage = ref(1)
    const pageSize = ref(10)
    const selectedRowKeys = ref([])
    
    // 計算屬性
    const computedColumns = computed(() => {
      return props.columns.map(column => {
        // 如果列有插槽，添加渲染函數
        if (slots[column.key]) {
          return {
            ...column,
            render: (row, index) => slots[column.key]({ row, index, column })
          }
        }
        return column
      })
    })
    
    const computedPagination = computed(() => {
      if (!props.pagination) return false
      
      const defaultPagination = {
        page: currentPage.value,
        pageSize: pageSize.value,
        showSizePicker: true,
        pageSizes: [10, 20, 50, 100],
        showQuickJumper: true,
        prefix: ({ itemCount }) => `共 ${itemCount} 項`
      }
      
      if (typeof props.pagination === 'object') {
        return { ...defaultPagination, ...props.pagination }
      }
      
      return defaultPagination
    })
    
    const tableContainerClasses = computed(() => {
      const classes = ['table-container']
      
      if (props.shadow !== 'none') {
        classes.push(`shadow-${props.shadow}`)
      }
      
      if (props.rounded !== 'none') {
        classes.push(`rounded-${props.rounded}`)
      }
      
      return classes
    })
    
    const tableClasses = computed(() => {
      const classes = ['base-table']
      
      classes.push(`table-${props.variant}`)
      
      return classes
    })
    
    // 動態插槽
    const dynamicSlots = computed(() => {
      return props.columns.filter(column => slots[column.key])
    })
    
    // 計算總項目數
    const totalItems = computed(() => props.data.length)
    
    // 計算選中項目數
    const selectedCount = computed(() => {
      return props.checkedRowKeys ? props.checkedRowKeys.length : selectedRowKeys.value.length
    })
    
    // 計算總頁數
    const totalPages = computed(() => {
      if (!props.pagination) return 1
      const size = typeof props.pagination === 'object' ? props.pagination.pageSize || pageSize.value : pageSize.value
      return Math.ceil(totalItems.value / size)
    })
    
    // 事件處理
    const handleCheckedRowKeysChange = (keys) => {
      selectedRowKeys.value = keys
      emit('update:checked-row-keys', keys)
    }
    
    const handleExpandedRowKeysChange = (keys) => {
      emit('update:expanded-row-keys', keys)
    }
    
    const handlePageChange = (page) => {
      currentPage.value = page
      emit('update:page', page)
    }
    
    const handlePageSizeChange = (size) => {
      pageSize.value = size
      emit('update:page-size', size)
    }
    
    const handleSorterChange = (sorter) => {
      emit('update:sorter', sorter)
    }
    
    const handleFiltersChange = (filters) => {
      emit('update:filters', filters)
    }
    
    return {
      computedColumns,
      computedPagination,
      tableContainerClasses,
      tableClasses,
      dynamicSlots,
      totalItems,
      selectedCount,
      totalPages,
      currentPage,
      handleCheckedRowKeysChange,
      handleExpandedRowKeysChange,
      handlePageChange,
      handlePageSizeChange,
      handleSorterChange,
      handleFiltersChange
    }
  }
}
</script>

<style scoped>
.base-table-container {
  @apply w-full;
}

.table-container {
  @apply bg-white overflow-hidden;
}

.base-table {
  @apply w-full;
}

.table-default {
  /* 預設樣式 */
}

.table-simple {
  /* 簡單樣式 */
}

.table-bordered {
  @apply border border-gray-200;
}

.table-striped {
  /* 條紋樣式 */
}

.table-header {
  @apply border-b border-gray-100 pb-4;
}

.table-footer {
  @apply border-t border-gray-100 pt-4;
}

/* 深色模式支援 */
.dark .table-container {
  @apply bg-gray-800;
}

.dark .table-header {
  @apply border-gray-700;
}

.dark .table-footer {
  @apply border-gray-700;
}

.dark .table-bordered {
  @apply border-gray-700;
}
</style>
