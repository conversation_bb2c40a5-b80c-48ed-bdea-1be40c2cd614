<template>
  <n-card
    :class="cardClasses"
    :hoverable="hoverable"
    :embedded="embedded"
    :bordered="bordered"
    :size="size"
    :segmented="segmented"
    :content-style="contentStyle"
    :header-style="headerStyle"
    :footer-style="footerStyle"
    v-bind="$attrs"
  >
    <!-- 標題插槽 -->
    <template #header v-if="$slots.header || title">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <!-- 圖示 -->
          <div v-if="icon" class="flex-shrink-0">
            <n-icon :size="iconSize" :color="iconColor">
              <component :is="icon" />
            </n-icon>
          </div>
          <!-- 標題 -->
          <div>
            <h3 v-if="title" :class="titleClasses">{{ title }}</h3>
            <p v-if="subtitle" :class="subtitleClasses">{{ subtitle }}</p>
          </div>
        </div>
        <!-- 標題右側插槽 -->
        <div v-if="$slots.headerExtra" class="flex-shrink-0">
          <slot name="headerExtra" />
        </div>
      </div>
      <!-- 自定義標題插槽 -->
      <slot name="header" />
    </template>

    <!-- 標題下方插槽 -->
    <template #header-extra v-if="$slots.headerExtra && !($slots.header || title)">
      <slot name="headerExtra" />
    </template>

    <!-- 主要內容 -->
    <div :class="bodyClasses">
      <slot />
    </div>

    <!-- 頁腳插槽 -->
    <template #footer v-if="$slots.footer">
      <div :class="footerClasses">
        <slot name="footer" />
      </div>
    </template>

    <!-- 動作插槽 -->
    <template #action v-if="$slots.action">
      <div class="flex items-center justify-end space-x-3">
        <slot name="action" />
      </div>
    </template>
  </n-card>
</template>

<script>
import { computed } from 'vue'
import { NCard, NIcon } from 'naive-ui'

export default {
  name: 'BaseCard',
  components: {
    NCard,
    NIcon
  },
  inheritAttrs: false,
  props: {
    // 基本屬性
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: [String, Object],
      default: null
    },
    iconSize: {
      type: [String, Number],
      default: 20
    },
    iconColor: {
      type: String,
      default: ''
    },
    
    // Naive UI 卡片屬性
    hoverable: {
      type: Boolean,
      default: false
    },
    embedded: {
      type: Boolean,
      default: false
    },
    bordered: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large', 'huge'].includes(value)
    },
    segmented: {
      type: [Boolean, Object],
      default: false
    },
    
    // 樣式自定義
    variant: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'primary', 'success', 'warning', 'error', 'info'].includes(value)
    },
    shadow: {
      type: String,
      default: 'md',
      validator: (value) => ['none', 'sm', 'md', 'lg', 'xl', '2xl'].includes(value)
    },
    rounded: {
      type: String,
      default: '2xl',
      validator: (value) => ['none', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', 'full'].includes(value)
    },
    padding: {
      type: String,
      default: 'normal',
      validator: (value) => ['none', 'sm', 'normal', 'lg', 'xl'].includes(value)
    },
    
    // 內容樣式
    contentStyle: {
      type: [String, Object],
      default: ''
    },
    headerStyle: {
      type: [String, Object],
      default: ''
    },
    footerStyle: {
      type: [String, Object],
      default: ''
    }
  },
  
  setup(props) {
    // 計算卡片樣式類別
    const cardClasses = computed(() => {
      const classes = ['base-card']
      
      // 變體樣式
      if (props.variant !== 'default') {
        classes.push(`card-${props.variant}`)
      }
      
      // 陰影
      if (props.shadow !== 'none') {
        classes.push(`shadow-${props.shadow}`)
      }
      
      // 圓角
      if (props.rounded !== 'none') {
        classes.push(`rounded-${props.rounded}`)
      }
      
      return classes
    })
    
    // 計算標題樣式類別
    const titleClasses = computed(() => {
      const classes = ['text-lg font-semibold text-gray-900']
      
      if (props.variant === 'primary') {
        classes.push('text-primary-700')
      } else if (props.variant === 'success') {
        classes.push('text-success-700')
      } else if (props.variant === 'warning') {
        classes.push('text-warning-700')
      } else if (props.variant === 'error') {
        classes.push('text-error-700')
      } else if (props.variant === 'info') {
        classes.push('text-blue-700')
      }
      
      return classes
    })
    
    // 計算副標題樣式類別
    const subtitleClasses = computed(() => [
      'text-sm text-gray-500 mt-1'
    ])
    
    // 計算內容區域樣式類別
    const bodyClasses = computed(() => {
      const classes = []
      
      // 內邊距
      if (props.padding === 'none') {
        classes.push('p-0')
      } else if (props.padding === 'sm') {
        classes.push('p-3')
      } else if (props.padding === 'normal') {
        classes.push('p-4')
      } else if (props.padding === 'lg') {
        classes.push('p-6')
      } else if (props.padding === 'xl') {
        classes.push('p-8')
      }
      
      return classes
    })
    
    // 計算頁腳樣式類別
    const footerClasses = computed(() => [
      'border-t border-gray-100 bg-gray-50 px-4 py-3'
    ])
    
    return {
      cardClasses,
      titleClasses,
      subtitleClasses,
      bodyClasses,
      footerClasses
    }
  }
}
</script>

<style scoped>
.base-card {
  @apply transition-all duration-200;
}

.card-primary {
  @apply border-primary-200 bg-primary-50;
}

.card-success {
  @apply border-success-200 bg-success-50;
}

.card-warning {
  @apply border-warning-200 bg-warning-50;
}

.card-error {
  @apply border-error-200 bg-error-50;
}

.card-info {
  @apply border-blue-200 bg-blue-50;
}

/* 深色模式支援 */
.dark .base-card {
  @apply bg-gray-800 border-gray-700;
}

.dark .card-primary {
  @apply border-primary-700 bg-primary-900;
}

.dark .card-success {
  @apply border-success-700 bg-success-900;
}

.dark .card-warning {
  @apply border-warning-700 bg-warning-900;
}

.dark .card-error {
  @apply border-error-700 bg-error-900;
}

.dark .card-info {
  @apply border-blue-700 bg-blue-900;
}
</style>
