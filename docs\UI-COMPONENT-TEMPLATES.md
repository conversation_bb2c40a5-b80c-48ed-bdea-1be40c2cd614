# 🧱 UI 元件模板文檔

## 📋 概述

本文檔提供了基於 Naive UI + Tailwind CSS 的 Vue 3 元件模板，用於 PLC 能源管理系統的 UI 重構。所有元件都遵循統一的設計系統和編碼規範。

## 🎨 基礎元件模板

### BaseCard.vue - 資訊卡片元件
```vue
<template>
  <div 
    :class="cardClasses"
    @click="handleClick"
  >
    <!-- 卡片標題 -->
    <div v-if="title || $slots.header" class="card-header">
      <slot name="header">
        <h3 class="text-lg font-semibold text-slate-800">{{ title }}</h3>
      </slot>
      <div v-if="$slots.actions" class="card-actions">
        <slot name="actions"></slot>
      </div>
    </div>

    <!-- 卡片內容 -->
    <div class="card-body">
      <slot></slot>
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="card-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'BaseCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    variant: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'bordered', 'shadow', 'hover'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    clickable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const cardClasses = computed(() => {
      const baseClasses = 'bg-white rounded-2xl transition-all duration-200'
      const variantClasses = {
        default: 'border border-gray-100',
        bordered: 'border-2 border-gray-200',
        shadow: 'shadow-lg border border-gray-100',
        hover: 'shadow-md hover:shadow-xl border border-gray-100'
      }
      const sizeClasses = {
        small: 'p-4',
        medium: 'p-6',
        large: 'p-8'
      }
      const clickableClasses = props.clickable ? 'cursor-pointer hover:bg-gray-50' : ''

      return [
        baseClasses,
        variantClasses[props.variant],
        sizeClasses[props.size],
        clickableClasses
      ].join(' ')
    })

    const handleClick = (event) => {
      if (props.clickable) {
        emit('click', event)
      }
    }

    return {
      cardClasses,
      handleClick
    }
  }
})
</script>

<style scoped>
.card-header {
  @apply flex items-center justify-between mb-4 pb-4 border-b border-gray-100;
}

.card-actions {
  @apply flex items-center space-x-2;
}

.card-body {
  @apply flex-1;
}

.card-footer {
  @apply mt-4 pt-4 border-t border-gray-100;
}
</style>
```

### BaseButton.vue - 標準化按鈕
```vue
<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <!-- 載入圖示 -->
    <n-spin v-if="loading" :size="16" class="mr-2" />
    
    <!-- 圖示 -->
    <component 
      v-else-if="icon" 
      :is="icon" 
      :size="iconSize" 
      class="mr-2" 
    />

    <!-- 按鈕文字 -->
    <span v-if="$slots.default || text">
      <slot>{{ text }}</slot>
    </span>
  </button>
</template>

<script>
import { defineComponent, computed } from 'vue'
import { NSpin } from 'naive-ui'

export default defineComponent({
  name: 'BaseButton',
  components: {
    NSpin
  },
  props: {
    text: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'default',
      validator: (value) => ['primary', 'secondary', 'success', 'warning', 'error', 'default'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    variant: {
      type: String,
      default: 'filled',
      validator: (value) => ['filled', 'outlined', 'text', 'ghost'].includes(value)
    },
    icon: {
      type: [String, Object],
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    block: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const buttonClasses = computed(() => {
      const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2'
      
      const typeClasses = {
        primary: {
          filled: 'bg-orange-500 text-white hover:bg-orange-600 focus:ring-orange-500',
          outlined: 'border-2 border-orange-500 text-orange-500 hover:bg-orange-50 focus:ring-orange-500',
          text: 'text-orange-500 hover:bg-orange-50 focus:ring-orange-500',
          ghost: 'text-orange-500 hover:bg-orange-50 focus:ring-orange-500'
        },
        secondary: {
          filled: 'bg-slate-600 text-white hover:bg-slate-700 focus:ring-slate-500',
          outlined: 'border-2 border-slate-600 text-slate-600 hover:bg-slate-50 focus:ring-slate-500',
          text: 'text-slate-600 hover:bg-slate-50 focus:ring-slate-500',
          ghost: 'text-slate-600 hover:bg-slate-50 focus:ring-slate-500'
        },
        success: {
          filled: 'bg-emerald-500 text-white hover:bg-emerald-600 focus:ring-emerald-500',
          outlined: 'border-2 border-emerald-500 text-emerald-500 hover:bg-emerald-50 focus:ring-emerald-500',
          text: 'text-emerald-500 hover:bg-emerald-50 focus:ring-emerald-500',
          ghost: 'text-emerald-500 hover:bg-emerald-50 focus:ring-emerald-500'
        },
        warning: {
          filled: 'bg-amber-500 text-white hover:bg-amber-600 focus:ring-amber-500',
          outlined: 'border-2 border-amber-500 text-amber-500 hover:bg-amber-50 focus:ring-amber-500',
          text: 'text-amber-500 hover:bg-amber-50 focus:ring-amber-500',
          ghost: 'text-amber-500 hover:bg-amber-50 focus:ring-amber-500'
        },
        error: {
          filled: 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500',
          outlined: 'border-2 border-red-500 text-red-500 hover:bg-red-50 focus:ring-red-500',
          text: 'text-red-500 hover:bg-red-50 focus:ring-red-500',
          ghost: 'text-red-500 hover:bg-red-50 focus:ring-red-500'
        },
        default: {
          filled: 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500',
          outlined: 'border-2 border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500',
          text: 'text-gray-700 hover:bg-gray-50 focus:ring-gray-500',
          ghost: 'text-gray-700 hover:bg-gray-50 focus:ring-gray-500'
        }
      }

      const sizeClasses = {
        small: 'px-3 py-1.5 text-sm',
        medium: 'px-4 py-2 text-base',
        large: 'px-6 py-3 text-lg'
      }

      const disabledClasses = props.disabled || props.loading ? 'opacity-50 cursor-not-allowed' : ''
      const blockClasses = props.block ? 'w-full' : ''

      return [
        baseClasses,
        typeClasses[props.type][props.variant],
        sizeClasses[props.size],
        disabledClasses,
        blockClasses
      ].join(' ')
    })

    const iconSize = computed(() => {
      const sizes = {
        small: 14,
        medium: 16,
        large: 18
      }
      return sizes[props.size]
    })

    const handleClick = (event) => {
      if (!props.disabled && !props.loading) {
        emit('click', event)
      }
    }

    return {
      buttonClasses,
      iconSize,
      handleClick
    }
  }
})
</script>
```

### BaseTable.vue - 資料表格元件
```vue
<template>
  <div class="table-container">
    <!-- 表格工具列 -->
    <div v-if="showToolbar" class="table-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <h3 v-if="title" class="text-lg font-semibold text-slate-800">{{ title }}</h3>
        </slot>
      </div>
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <!-- 搜尋框 -->
          <n-input
            v-if="searchable"
            v-model:value="searchQuery"
            placeholder="搜尋..."
            clearable
            class="w-64"
          >
            <template #prefix>
              <n-icon :component="SearchIcon" />
            </template>
          </n-input>
        </slot>
      </div>
    </div>

    <!-- 表格主體 -->
    <div class="table-wrapper">
      <n-data-table
        :columns="computedColumns"
        :data="filteredData"
        :loading="loading"
        :pagination="paginationConfig"
        :row-key="rowKey"
        :scroll-x="scrollX"
        :size="size"
        :striped="striped"
        :bordered="bordered"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:sorter="handleSorterChange"
      />
    </div>
  </div>
</template>

<script>
import { defineComponent, computed, ref, watch } from 'vue'
import { NDataTable, NInput, NIcon } from 'naive-ui'
import { SearchOutline as SearchIcon } from '@vicons/ionicons5'

export default defineComponent({
  name: 'BaseTable',
  components: {
    NDataTable,
    NInput,
    NIcon
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    columns: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    rowKey: {
      type: [String, Function],
      default: 'id'
    },
    pagination: {
      type: [Boolean, Object],
      default: true
    },
    searchable: {
      type: Boolean,
      default: true
    },
    showToolbar: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    scrollX: {
      type: Number,
      default: null
    }
  },
  emits: ['page-change', 'page-size-change', 'sorter-change'],
  setup(props, { emit }) {
    const searchQuery = ref('')

    const computedColumns = computed(() => {
      return props.columns.map(column => ({
        ...column,
        titleColSpan: column.titleColSpan || 1,
        align: column.align || 'left',
        ellipsis: column.ellipsis !== false
      }))
    })

    const filteredData = computed(() => {
      if (!props.searchable || !searchQuery.value) {
        return props.data
      }

      const query = searchQuery.value.toLowerCase()
      return props.data.filter(row => {
        return Object.values(row).some(value => 
          String(value).toLowerCase().includes(query)
        )
      })
    })

    const paginationConfig = computed(() => {
      if (!props.pagination) return false
      
      const defaultConfig = {
        page: 1,
        pageSize: 20,
        showSizePicker: true,
        pageSizes: [10, 20, 50, 100],
        showQuickJumper: true,
        prefix: ({ itemCount }) => `共 ${itemCount} 項`
      }

      return typeof props.pagination === 'object' 
        ? { ...defaultConfig, ...props.pagination }
        : defaultConfig
    })

    const handlePageChange = (page) => {
      emit('page-change', page)
    }

    const handlePageSizeChange = (pageSize) => {
      emit('page-size-change', pageSize)
    }

    const handleSorterChange = (sorter) => {
      emit('sorter-change', sorter)
    }

    return {
      SearchIcon,
      searchQuery,
      computedColumns,
      filteredData,
      paginationConfig,
      handlePageChange,
      handlePageSizeChange,
      handleSorterChange
    }
  }
})
</script>

<style scoped>
.table-container {
  @apply bg-white rounded-2xl shadow-lg overflow-hidden;
}

.table-toolbar {
  @apply flex items-center justify-between p-6 border-b border-gray-100;
}

.toolbar-left {
  @apply flex items-center space-x-4;
}

.toolbar-right {
  @apply flex items-center space-x-4;
}

.table-wrapper {
  @apply overflow-auto;
}
</style>
```

## 📊 圖表元件模板

### BaseChart.vue - ECharts 圖表封裝
```vue
<template>
  <BaseCard :title="title" :variant="cardVariant">
    <div 
      ref="chartRef" 
      :style="{ width: width, height: height }"
      class="chart-container"
    />
    
    <template #actions>
      <slot name="actions">
        <BaseButton
          v-if="downloadable"
          type="default"
          variant="text"
          size="small"
          @click="downloadChart"
        >
          下載
        </BaseButton>
      </slot>
    </template>
  </BaseCard>
</template>

<script>
import { defineComponent, ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import BaseCard from './BaseCard.vue'
import BaseButton from './BaseButton.vue'

export default defineComponent({
  name: 'BaseChart',
  components: {
    BaseCard,
    BaseButton
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    options: {
      type: Object,
      required: true
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '400px'
    },
    theme: {
      type: String,
      default: 'default'
    },
    cardVariant: {
      type: String,
      default: 'shadow'
    },
    downloadable: {
      type: Boolean,
      default: true
    },
    autoResize: {
      type: Boolean,
      default: true
    }
  },
  emits: ['chart-ready', 'chart-click'],
  setup(props, { emit }) {
    const chartRef = ref(null)
    let chartInstance = null
    let resizeObserver = null

    const initChart = async () => {
      if (!chartRef.value) return

      await nextTick()
      
      chartInstance = echarts.init(chartRef.value, props.theme)
      chartInstance.setOption(props.options)

      // 綁定事件
      chartInstance.on('click', (params) => {
        emit('chart-click', params)
      })

      emit('chart-ready', chartInstance)

      // 自動調整大小
      if (props.autoResize) {
        setupResize()
      }
    }

    const setupResize = () => {
      if (window.ResizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          if (chartInstance) {
            chartInstance.resize()
          }
        })
        resizeObserver.observe(chartRef.value)
      } else {
        window.addEventListener('resize', handleResize)
      }
    }

    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    const downloadChart = () => {
      if (chartInstance) {
        const url = chartInstance.getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff'
        })
        
        const link = document.createElement('a')
        link.download = `${props.title || 'chart'}.png`
        link.href = url
        link.click()
      }
    }

    const updateChart = () => {
      if (chartInstance) {
        chartInstance.setOption(props.options, true)
      }
    }

    watch(() => props.options, updateChart, { deep: true })

    onMounted(() => {
      initChart()
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      if (resizeObserver) {
        resizeObserver.disconnect()
      } else {
        window.removeEventListener('resize', handleResize)
      }
    })

    return {
      chartRef,
      downloadChart
    }
  }
})
</script>

<style scoped>
.chart-container {
  @apply w-full;
}
</style>
```

## 🎯 使用範例

### 基本卡片使用
```vue
<template>
  <BaseCard 
    title="能耗統計" 
    variant="shadow" 
    size="medium"
    clickable
    @click="handleCardClick"
  >
    <template #actions>
      <BaseButton type="primary" size="small">設定</BaseButton>
    </template>
    
    <div class="text-3xl font-bold text-orange-500">1,234 kWh</div>
    <div class="text-sm text-gray-500 mt-2">本月累計</div>
    
    <template #footer>
      <div class="text-xs text-gray-400">最後更新：2024-01-15 14:30</div>
    </template>
  </BaseCard>
</template>
```

### 資料表格使用
```vue
<template>
  <BaseTable
    title="設備列表"
    :columns="columns"
    :data="devices"
    :loading="loading"
    searchable
    @page-change="handlePageChange"
  >
    <template #toolbar-right>
      <BaseButton type="primary" @click="addDevice">
        新增設備
      </BaseButton>
    </template>
  </BaseTable>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { title: '設備名稱', key: 'name', sortable: true },
        { title: '狀態', key: 'status', render: (row) => this.renderStatus(row.status) },
        { title: '位置', key: 'location' },
        { title: '操作', key: 'actions', render: (row) => this.renderActions(row) }
      ],
      devices: [],
      loading: false
    }
  }
}
</script>
```

---

*這些模板提供了統一的設計語言和開發模式，確保整個系統的一致性和可維護性。*
